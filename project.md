# 本地宝 - O2O本地生活服务小程序项目

## 项目概述

本项目是一个基于uni-app + Vue3 + TypeScript的跨平台本地生活服务小程序，主要面向三四线城市和县城用户以及普工群体，提供求职招聘、房产服务、同城交友、零工派遣等核心功能。

## 前端技术栈
- **框架**: Vue 3 + TypeScript + uni-app
- **状态管理**: Pinia
- **UI组件**: uni-ui、ThorUI、uvui
- **样式**: UnoCSS + SCSS
- **网络请求**: @uni-helper/uni-network
- **分页组件**: z-paging

## 核心功能模块分析

### 1. 首页模块 (Home)
**页面路径**: `pages/home/<USER>

**功能描述**: 应用入口页面，提供核心功能导航和服务分类入口

**主要功能**:
- 城市定位选择
- 搜索功能入口
- 核心功能导航（求职招聘、二手房、租房、新房、交友）
- 生活服务宫格（零工、管道疏通、家居装修等10个服务分类）
- 推荐内容展示
- 发布悬浮按钮

**数据字段**:
- 当前城市信息 (currentCity)
- 搜索占位符 (searchPlaceholder)
- 通知状态 (hasNotification)
- 导航项配置 (coreNavItems: title, icon, path, linkPath)
- 服务分类配置 (services: name, imagePath, color, path)

### 2. 求职招聘模块 (Job)
**子包路径**: `pages/job/*`

**功能描述**: 完整的招聘求职生态系统，支持求职者和招聘者双重角色

#### 2.1 求职者功能
**页面**: `pages/job/jobseeker/index.vue`
- 职位搜索和筛选 (experience, education, salary, jobType, welfare)
- 简历管理 (resume/resume.vue, resume/edit.vue)
- 投递记录跟踪
- 收藏职位管理
- 意向职位设置 (intention-edit.vue)

#### 2.2 招聘者功能  
**页面**: `pages/job/recruiter/index.vue`
- 人才库搜索和筛选
- 职位发布管理 (publish.vue)
- 职位管理 (manage.vue)
- 简历投递管理
- 数据分析 (analytics.vue)
- 人才详情查看 (talent-detail.vue)

#### 2.3 企业服务
**页面**: `pages/job/company-auth.vue`, `pages/job/enterprise-cooperation.vue`
- 企业认证流程
- 企业合作服务
- 营业执照上传验证

### 3. 房产模块 (House)
**子包路径**: `pages/house/*`

**功能描述**: 全方位房产服务平台，涵盖租房、二手房、新房、商业地产

#### 3.1 租房功能
**页面**: `pages/house/rent/index.vue`
- 房源列表展示和搜索
- 多维度筛选 (area, rentType, priceRange, etc.)
- 房源详情查看 (detail/index.vue)
- 发布租房信息 (publish/rent.vue)
- 租房管理 (manage/rent.vue)

#### 3.2 二手房功能
**页面**: `pages/house/secondHouse/index.vue`
- 二手房源搜索浏览
- 价格走势分析
- 房源对比功能
- 发布二手房 (publish/second-hand.vue)

#### 3.3 新房功能
**页面**: `pages/house/newHouse/index.vue`, `pages/house/newHouse/detail.vue`
- 楼盘信息展示
- 开盘提醒功能
- 价格监控
- 发布新房项目 (publish/new-house.vue)

#### 3.4 商业地产
**页面**: `pages/house/detail/commercial.vue`
- 商铺/写字楼信息
- 投资回报分析
- 发布商业地产 (publish/commercial.vue)

### 4. 同城交友模块 (Dating)
**子包路径**: `pages/dating/*`

**功能描述**: 本地化社交平台，支持用户互动和匹配

#### 4.1 交友主页
**页面**: `pages/dating/index.vue`
- 用户推荐展示
- 快速匹配功能
- 个人档案展示

#### 4.2 交友广场
**页面**: `pages/dating/square/index.vue`, `pages/dating/square/detail.vue`
- 动态发布和浏览 (square/publish.vue)
- 话题互动 (square/topic.vue)
- 动态详情查看
- 点赞评论分享

#### 4.3 个人中心
**页面**: `pages/dating/profile.vue`, `pages/dating/user-detail.vue`
- 个人档案管理
- 偏好设置 (settings.vue)
- 匹配记录查看

### 5. 零工模块 (Gig)
**子包路径**: `pages/gig/*`

**功能描述**: 短期灵活就业平台

#### 5.1 零工广场
**页面**: `pages/gig/index.vue`
- 任务搜索和筛选
- 技能匹配推荐
- 任务详情查看 (detail.vue)

#### 5.2 任务管理
**页面**: `pages/gig/publish.vue`, `pages/gig/manage.vue`
- 发布零工任务
- 我的零工管理
- 报名管理 (applicants.vue)
- 零工日历 (calendar.vue)

#### 5.3 个人中心
**页面**: `pages/gig/profile.vue`
- 个人主页展示
- 技能认证
- 信用评价

### 6. 社区模块 (Post)
**页面路径**: `pages/post/*`

**功能描述**: 用户社区互动平台

#### 6.1 社区动态
**页面**: `pages/post/list.vue`
- 动态流展示
- 分类浏览
- 热门推荐

#### 6.2 发布功能
**页面**: `pages/post/publish.vue`, `pages/post/detail.vue`
- 发布动态内容
- 图片视频上传
- 话题标签选择
- 动态详情查看

### 7. 消息模块 (Message)
**页面路径**: `pages/message/*`

**功能描述**: 即时通讯系统

#### 7.1 会话管理
**页面**: `pages/message/conversation.vue`
- 会话列表展示
- 未读消息提醒
- 会话搜索

#### 7.2 聊天功能
**页面**: `pages/message/chat.vue`
- 实时聊天界面
- 多媒体消息支持
- 表情包功能
- 语音视频通话

### 8. 个人中心模块 (Mine)
**页面路径**: `pages/mine/*`

**功能描述**: 用户个人信息管理

#### 8.1 我的主页
**页面**: `pages/mine/mine.vue`
- 个人信息展示
- 功能快捷入口
- VIP状态显示
- 钱包余额

#### 8.2 设置管理
**页面**: `pages/mine/settings.vue`, `pages/mine/privacy.vue`, `pages/mine/notification.vue`
- 账号设置
- 隐私设置
- 消息通知设置
- 个人资料编辑 (profile.vue)

#### 8.3 其他功能
**页面**: `pages/mine/collections.vue`, `pages/mine/about.vue`
- 我的收藏
- 关于我们
- 用户协议 (pages/auth/user-agreement.vue)
- 隐私政策 (pages/auth/privacy-policy.vue)

### 9. 发布模块 (Publish)
**页面路径**: `pages/publish/*`

**功能描述**: 统一发布入口

#### 9.1 发布中心
**页面**: `pages/publish/index.vue`
- 发布类型选择
- 快捷发布入口

#### 9.2 发布管理
**页面**: `pages/publish/manage.vue`
- 我的发布内容
- 发布状态管理
- 数据统计

### 10. 认证模块 (Auth)
**页面路径**: `pages/auth/*`

**功能描述**: 用户认证系统

#### 10.1 登录注册
**页面**: `pages/auth/login.vue`
- 手机号登录
- 微信授权登录
- 验证码验证

## 后端服务架构设计

### 技术栈选择
- **开发语言**: Go 1.21+
- **Web框架**: Gin
- **数据库**: PostgreSQL 15+ + Redis 7+
- **ORM**: GORM v2
- **实时通信**: Centrifugo（WebSocket/聊天/通知）
- **支付**: 微信支付 v3
- **短信服务**: 阿里云短信/腾讯云短信
- **JWT认证**: golang-jwt
- **参数验证**: validator/v10
- **容器化**: Docker + Docker Compose
- **日志**: Zerolog
- **API文档**: Swagger
- **配置管理**: Viper
- **缓存**: Redis

### 优化后的目录结构设计

```
fnbdb-backend/
├── cmd/                           # 命令行工具
│   ├── server/                    # 主服务
│   │   └── main.go
│   ├── migrate/                   # 数据库迁移工具
│   │   └── main.go
│   └── seeder/                    # 数据填充工具
│       └── main.go
├── internal/                      # 内部包，不对外暴露
│   ├── config/                    # 配置管理
│   │   ├── config.go              # 主配置
│   │   ├── database.go            # 数据库配置
│   │   ├── redis.go               # Redis配置
│   │   ├── centrifugo.go          # Centrifugo配置
│   │   ├── payment.go             # 支付配置
│   │   └── sms.go                 # 短信配置
│   ├── models/                    # 数据模型
│   │   ├── base.go                # 基础模型
│   │   ├── user.go                # 用户相关模型
│   │   ├── job.go                 # 招聘相关模型
│   │   ├── house.go               # 房产相关模型
│   │   ├── dating.go              # 交友相关模型
│   │   ├── gig.go                 # 零工相关模型
│   │   ├── community.go           # 社区相关模型
│   │   ├── message.go             # 消息相关模型
│   │   ├── payment.go             # 支付相关模型
│   │   └── system.go              # 系统相关模型
│   ├── controllers/               # HTTP控制器
│   │   ├── auth/                  # 认证相关
│   │   │   ├── login.go
│   │   │   ├── register.go
│   │   │   └── sms.go
│   │   ├── user/                  # 用户相关
│   │   │   ├── profile.go
│   │   │   ├── avatar.go
│   │   │   ├── wallet.go
│   │   │   └── verification.go
│   │   ├── job/                   # 招聘相关
│   │   │   ├── job.go
│   │   │   ├── resume.go
│   │   │   ├── application.go
│   │   │   └── company.go
│   │   ├── house/                 # 房产相关
│   │   │   ├── house.go
│   │   │   ├── rent.go
│   │   │   ├── sale.go
│   │   │   └── favorite.go
│   │   ├── dating/                # 交友相关
│   │   │   ├── profile.go
│   │   │   ├── match.go
│   │   │   ├── post.go
│   │   │   └── interaction.go
│   │   ├── gig/                   # 零工相关
│   │   │   ├── task.go
│   │   │   ├── application.go
│   │   │   └── rating.go
│   │   ├── community/             # 社区相关
│   │   │   ├── post.go
│   │   │   ├── comment.go
│   │   │   └── topic.go
│   │   ├── message/               # 消息相关
│   │   │   ├── conversation.go
│   │   │   └── notification.go
│   │   ├── payment/               # 支付相关
│   │   │   ├── order.go
│   │   │   ├── wechat.go
│   │   │   └── wallet.go
│   │   └── common/                # 通用接口
│   │       ├── upload.go
│   │       ├── region.go
│   │       └── search.go
│   ├── logic/                     # 业务逻辑层
│   │   ├── auth_logic.go          # 认证逻辑
│   │   ├── user_logic.go          # 用户逻辑
│   │   ├── job_logic.go           # 招聘逻辑
│   │   ├── house_logic.go         # 房产逻辑
│   │   ├── dating_logic.go        # 交友逻辑
│   │   ├── gig_logic.go           # 零工逻辑
│   │   ├── community_logic.go     # 社区逻辑
│   │   ├── message_logic.go       # 消息逻辑
│   │   ├── payment_logic.go       # 支付逻辑
│   │   ├── notification_logic.go  # 通知逻辑
│   │   ├── sms_logic.go           # 短信逻辑
│   │   └── upload_logic.go        # 上传逻辑
│   ├── repositories/              # 数据访问层
│   │   ├── user_repo.go
│   │   ├── job_repo.go
│   │   ├── house_repo.go
│   │   ├── dating_repo.go
│   │   ├── gig_repo.go
│   │   ├── community_repo.go
│   │   ├── message_repo.go
│   │   └── payment_repo.go
│   ├── middleware/                # 中间件
│   │   ├── auth.go                # JWT认证
│   │   ├── cors.go                # 跨域处理
│   │   ├── rate_limit.go          # 限流
│   │   ├── logger.go              # 请求日志
│   │   ├── recovery.go            # 错误恢复
│   │   ├── validator.go           # 参数验证
│   │   └── centrifugo.go          # Centrifugo中间件
│   ├── dto/                       # 数据传输对象
│   │   ├── request/               # 请求DTO
│   │   └── response/              # 响应DTO
│   ├── utils/                     # 工具函数
│   │   ├── jwt.go                 # JWT工具
│   │   ├── validator.go           # 验证器
│   │   ├── password.go            # 密码加密
│   │   ├── sms.go                 # 短信工具
│   │   ├── payment.go             # 支付工具
│   │   ├── image.go               # 图片处理
│   │   ├── location.go            # 地理位置
│   │   └── response.go            # 响应工具
│   └── pkg/                       # 公共包
│       ├── database/              # 数据库
│       │   ├── postgres.go
│       │   └── redis.go
│       ├── logger/                # Zerolog日志
│       │   └── logger.go
│       ├── centrifugo/            # Centrifugo客户端
│       │   └── client.go
│       └── cache/                 # 缓存
│           └── redis.go
├── api/                           # API文档
│   └── swagger/                   # Swagger文档
│       ├── docs.go
│       ├── swagger.json
│       └── swagger.yaml
├── scripts/                       # 脚本文件
│   ├── migration/                 # 数据库迁移
│   │   ├── 001_init.up.sql
│   │   ├── 001_init.down.sql
│   │   ├── 002_add_indexes.up.sql
│   │   └── 002_add_indexes.down.sql
│   ├── deployment/                # 部署脚本
│   │   ├── deploy.sh
│   │   └── health_check.sh
│   └── seed/                      # 初始数据
│       ├── regions.sql
│       ├── categories.sql
│       └── test_data.sql
├── configs/                       # 配置文件
│   ├── config.yaml               # 主配置文件
│   ├── config.dev.yaml           # 开发环境
│   ├── config.prod.yaml          # 生产环境
│   ├── docker-compose.yml        # Docker编排
│   └── centrifugo.json           # Centrifugo配置
├── docs/                         # 文档
│   ├── api.md                    # API文档
│   ├── database.md               # 数据库设计
│   ├── deployment.md             # 部署文档
│   └── development.md            # 开发文档
├── tests/                        # 测试
│   ├── integration/              # 集成测试
│   ├── unit/                     # 单元测试
│   └── fixtures/                 # 测试数据
├── go.mod                        # Go模块
├── go.sum                        # Go模块校验
├── Dockerfile                    # Docker镜像
├── .env.example                  # 环境变量示例
├── Makefile                      # Make命令
└── README.md                     # 项目说明

## 数据库设计

### 数据库选择说明
- **PostgreSQL**: 主数据库，支持复杂查询、JSON类型、地理位置查询
- **Redis**: 缓存、会话存储、排行榜、分布式锁
- **分库分表策略**: 按城市ID进行分库，大表按时间或ID进行分表

### 核心表结构设计（含详细字段备注）

#### 1. 地区和分类相关表

```sql
-- 地区表
CREATE TABLE regions (
    id SERIAL PRIMARY KEY,                    -- 地区ID
    parent_id INTEGER REFERENCES regions(id), -- 父级地区ID
    name VARCHAR(100) NOT NULL,              -- 地区名称
    code VARCHAR(20),                        -- 地区编码
    level INTEGER NOT NULL,                  -- 级别：1省份/2城市/3区县
    is_hot BOOLEAN DEFAULT FALSE,            -- 是否热门城市
    sort_order INTEGER DEFAULT 0,           -- 排序
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE regions IS '地区表，支持省市区三级结构';
COMMENT ON COLUMN regions.level IS '级别：1省份/2城市/3区县';

-- 职位分类表
CREATE TABLE job_categories (
    id SERIAL PRIMARY KEY,                   -- 分类ID
    parent_id INTEGER REFERENCES job_categories(id), -- 父级分类ID
    name VARCHAR(100) NOT NULL,             -- 分类名称
    icon VARCHAR(200),                      -- 分类图标
    sort_order INTEGER DEFAULT 0,          -- 排序
    is_active BOOLEAN DEFAULT TRUE,        -- 是否启用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE job_categories IS '职位分类表';

-- 房产分类表
CREATE TABLE house_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,             -- 分类名称：租房/二手房/新房/商业地产
    subcategories TEXT[],                   -- 子分类数组
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE house_categories IS '房产分类表';
```

#### 2. 用户相关表

```sql
-- 用户基础信息表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,                                    -- 用户ID，主键
    phone VARCHAR(20) UNIQUE NOT NULL,                          -- 手机号，唯一索引
    wechat_openid VARCHAR(100) UNIQUE,                          -- 微信openid，唯一索引
    wechat_unionid VARCHAR(100),                                -- 微信unionid
    nickname VARCHAR(50) NOT NULL DEFAULT '',                   -- 用户昵称
    real_name VARCHAR(50),                                      -- 真实姓名
    avatar VARCHAR(500),                                        -- 头像URL
    gender VARCHAR(10) DEFAULT 'unknown',                       -- 性别：male/female/unknown
    birth_date DATE,                                            -- 出生日期
    age INTEGER GENERATED ALWAYS AS (EXTRACT(YEAR FROM AGE(birth_date))) STORED, -- 年龄，计算字段
    id_card VARCHAR(18),                                        -- 身份证号
    location VARCHAR(100),                                      -- 所在地区
    address TEXT,                                               -- 详细地址
    longitude DECIMAL(10,7),                                    -- 经度
    latitude DECIMAL(10,7),                                     -- 纬度
    introduction TEXT,                                          -- 个人简介
    is_vip BOOLEAN DEFAULT FALSE,                              -- 是否VIP用户
    vip_expire_time TIMESTAMP,                                  -- VIP过期时间
    verification_level VARCHAR(20) DEFAULT 'none',             -- 认证级别：none/phone/identity/enterprise
    verification_data JSONB,                                   -- 认证相关数据，JSON格式
    balance DECIMAL(10,2) DEFAULT 0,                          -- 账户余额（单位：元）
    points INTEGER DEFAULT 0,                                  -- 积分
    last_login_time TIMESTAMP,                                 -- 最后登录时间
    last_login_ip INET,                                        -- 最后登录IP
    status VARCHAR(20) DEFAULT 'active',                       -- 用户状态：active/inactive/banned
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    deleted_at TIMESTAMP                                       -- 软删除时间
);
COMMENT ON TABLE users IS '用户基础信息表';
COMMENT ON COLUMN users.id IS '用户ID，主键';
COMMENT ON COLUMN users.phone IS '手机号，唯一索引，用于登录验证';
COMMENT ON COLUMN users.verification_level IS '认证级别：none未认证/phone手机认证/identity实名认证/enterprise企业认证';
COMMENT ON COLUMN users.balance IS '账户余额，单位：元，支持充值和消费';
COMMENT ON COLUMN users.status IS '用户状态：active正常/inactive停用/banned封禁';

-- 企业认证表
CREATE TABLE companies (
    id BIGSERIAL PRIMARY KEY,                                 -- 企业ID
    user_id BIGINT REFERENCES users(id),                     -- 关联用户ID
    company_name VARCHAR(200) NOT NULL,                      -- 企业名称
    business_license VARCHAR(100),                           -- 营业执照号
    license_image VARCHAR(500),                              -- 营业执照图片URL
    legal_person VARCHAR(50),                                -- 法人代表
    contact_person VARCHAR(50),                              -- 联系人
    contact_phone VARCHAR(20),                               -- 联系电话
    email VARCHAR(100),                                      -- 企业邮箱
    address TEXT,                                            -- 企业地址
    industry VARCHAR(50),                                    -- 所属行业
    scale VARCHAR(50),                                       -- 企业规模：10人以下/10-50人/50-200人/200+人
    description TEXT,                                        -- 企业描述
    logo VARCHAR(500),                                       -- 企业logo URL
    status VARCHAR(20) DEFAULT 'pending',                    -- 认证状态：pending待审核/approved已通过/rejected已拒绝
    audit_reason TEXT,                                       -- 审核原因（拒绝时填写）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,          -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP           -- 更新时间
);
COMMENT ON TABLE companies IS '企业认证表';
COMMENT ON COLUMN companies.status IS '认证状态：pending待审核/approved已通过/rejected已拒绝';
COMMENT ON COLUMN companies.scale IS '企业规模：10人以下/10-50人/50-200人/200+人';

-- 用户技能表
CREATE TABLE user_skills (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),    -- 用户ID
    skill_name VARCHAR(100) NOT NULL,       -- 技能名称
    skill_level VARCHAR(20),                -- 技能等级：初级/中级/高级/专家
    years_of_experience INTEGER DEFAULT 0,  -- 经验年限
    is_certified BOOLEAN DEFAULT FALSE,     -- 是否有认证
    certificate_url VARCHAR(500),           -- 认证证书URL
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE user_skills IS '用户技能表';

-- 用户偏好表（交友）
CREATE TABLE user_preferences (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),    -- 用户ID
    preferred_gender VARCHAR(20),           -- 偏好性别
    age_min INTEGER,                        -- 偏好最小年龄
    age_max INTEGER,                        -- 偏好最大年龄
    preferred_education VARCHAR(50),        -- 偏好学历
    preferred_height_min INTEGER,           -- 偏好最小身高(cm)
    preferred_height_max INTEGER,           -- 偏好最大身高(cm)
    preferred_location VARCHAR(100),        -- 偏好地区
    preferred_interests TEXT[],             -- 偏好兴趣标签数组
    dating_purpose VARCHAR(50),             -- 交友目的：恋爱/结婚/交友
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);
COMMENT ON TABLE user_preferences IS '用户交友偏好表';

-- 钱包流水表
CREATE TABLE wallet_transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),    -- 用户ID
    type VARCHAR(20) NOT NULL,              -- 类型：recharge充值/withdraw提现/consume消费/refund退款
    amount DECIMAL(10,2) NOT NULL,          -- 金额
    balance_after DECIMAL(10,2) NOT NULL,   -- 操作后余额
    description TEXT,                       -- 交易描述
    related_order_id BIGINT,                -- 关联订单ID
    status VARCHAR(20) DEFAULT 'completed', -- 状态：pending处理中/completed已完成/failed已失败
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE wallet_transactions IS '钱包流水表';
COMMENT ON COLUMN wallet_transactions.type IS '类型：recharge充值/withdraw提现/consume消费/refund退款';
```

#### 3. 招聘求职相关表

```sql
-- 职位信息表
CREATE TABLE jobs (
    id BIGSERIAL PRIMARY KEY,                                -- 职位ID
    company_id BIGINT REFERENCES companies(id),             -- 关联企业ID
    publisher_id BIGINT REFERENCES users(id),               -- 发布者ID
    title VARCHAR(200) NOT NULL,                            -- 职位名称
    description TEXT,                                        -- 职位描述
    requirements TEXT,                                       -- 任职要求
    salary_min INTEGER,                                      -- 最低薪资（月薪，单位：元）
    salary_max INTEGER,                                      -- 最高薪资（月薪，单位：元）
    salary_negotiable BOOLEAN DEFAULT FALSE,                -- 薪资是否面议
    location VARCHAR(100),                                   -- 工作地点（城市）
    address TEXT,                                           -- 详细工作地址
    job_type VARCHAR(20) DEFAULT 'full_time',               -- 工作类型：full_time全职/part_time兼职/internship实习
    experience_required VARCHAR(50),                        -- 经验要求：无经验/1年以下/1-3年/3-5年/5年以上
    education_required VARCHAR(50),                         -- 学历要求：不限/初中/高中/大专/本科/硕士及以上
    age_min INTEGER,                                        -- 最小年龄要求
    age_max INTEGER,                                        -- 最大年龄要求
    gender_required VARCHAR(20) DEFAULT 'unlimited',        -- 性别要求：male男性/female女性/unlimited不限
    welfare TEXT[],                                         -- 福利待遇数组：五险一金/包吃住/年终奖等
    tags TEXT[],                                           -- 职位标签数组：双休/可转正/环境好等
    contact_person VARCHAR(50),                             -- 联系人
    contact_phone VARCHAR(20),                              -- 联系电话
    is_urgent BOOLEAN DEFAULT FALSE,                        -- 是否紧急招聘
    recruit_num INTEGER DEFAULT 1,                         -- 招聘人数
    status VARCHAR(20) DEFAULT 'active',                    -- 状态：active招聘中/inactive已暂停/closed已关闭
    views INTEGER DEFAULT 0,                                -- 浏览量
    applications INTEGER DEFAULT 0,                         -- 申请人数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,         -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP          -- 更新时间
);
COMMENT ON TABLE jobs IS '职位信息表';
COMMENT ON COLUMN jobs.salary_min IS '最低薪资，月薪，单位：元';
COMMENT ON COLUMN jobs.salary_max IS '最高薪资，月薪，单位：元';
COMMENT ON COLUMN jobs.job_type IS '工作类型：full_time全职/part_time兼职/internship实习';
COMMENT ON COLUMN jobs.gender_required IS '性别要求：male男性/female女性/unlimited不限';
COMMENT ON COLUMN jobs.status IS '状态：active招聘中/inactive已暂停/closed已关闭';

-- 简历信息表
CREATE TABLE resumes (
    id BIGSERIAL PRIMARY KEY,                               -- 简历ID
    user_id BIGINT REFERENCES users(id),                   -- 用户ID
    name VARCHAR(50) NOT NULL,                             -- 姓名
    gender VARCHAR(10),                                    -- 性别：male/female
    birth_date DATE,                                       -- 出生日期
    phone VARCHAR(20),                                     -- 联系电话
    email VARCHAR(100),                                    -- 邮箱
    current_location VARCHAR(100),                         -- 现居地
    education VARCHAR(50),                                -- 最高学历
    major VARCHAR(100),                                   -- 专业
    work_experience INTEGER DEFAULT 0,                   -- 工作经验（年）
    skills TEXT[],                                        -- 技能标签数组
    expected_salary_min INTEGER,                          -- 期望薪资最小值（元）
    expected_salary_max INTEGER,                          -- 期望薪资最大值（元）
    expected_locations TEXT[],                            -- 期望工作地点数组
    career_objective TEXT,                               -- 求职意向
    self_introduction TEXT,                              -- 自我介绍
    is_public BOOLEAN DEFAULT FALSE,                     -- 是否公开简历
    is_default BOOLEAN DEFAULT TRUE,                     -- 是否默认简历
    completeness INTEGER DEFAULT 0,                     -- 简历完整度百分比
    views INTEGER DEFAULT 0,                            -- 浏览量
    status VARCHAR(20) DEFAULT 'active',                -- 状态：active正常/inactive停用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,     -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP      -- 更新时间
);
COMMENT ON TABLE resumes IS '简历信息表';
COMMENT ON COLUMN resumes.work_experience IS '工作经验，单位：年';
COMMENT ON COLUMN resumes.expected_salary_min IS '期望薪资最小值，单位：元';
COMMENT ON COLUMN resumes.completeness IS '简历完整度，百分比，0-100';

-- 工作经历表
CREATE TABLE work_experiences (
    id BIGSERIAL PRIMARY KEY,
    resume_id BIGINT REFERENCES resumes(id), -- 简历ID
    company_name VARCHAR(200) NOT NULL,      -- 公司名称
    position VARCHAR(100) NOT NULL,          -- 职位
    industry VARCHAR(100),                   -- 行业
    start_date DATE NOT NULL,                -- 开始日期
    end_date DATE,                           -- 结束日期（为空表示至今）
    is_current BOOLEAN DEFAULT FALSE,        -- 是否当前工作
    job_description TEXT,                    -- 工作描述
    achievements TEXT,                       -- 主要成就
    salary_range VARCHAR(50),                -- 薪资范围
    sort_order INTEGER DEFAULT 0,           -- 排序
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE work_experiences IS '工作经历表';

-- 教育经历表
CREATE TABLE education_experiences (
    id BIGSERIAL PRIMARY KEY,
    resume_id BIGINT REFERENCES resumes(id), -- 简历ID
    school_name VARCHAR(200) NOT NULL,       -- 学校名称
    degree VARCHAR(50),                      -- 学位：高中/大专/本科/硕士/博士
    major VARCHAR(100),                      -- 专业
    start_date DATE NOT NULL,                -- 开始日期
    end_date DATE,                           -- 结束日期
    gpa VARCHAR(10),                         -- 绩点/成绩
    description TEXT,                        -- 描述
    sort_order INTEGER DEFAULT 0,           -- 排序
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE education_experiences IS '教育经历表';

-- 求职申请表
CREATE TABLE job_applications (
    id BIGSERIAL PRIMARY KEY,                       -- 申请ID
    job_id BIGINT REFERENCES jobs(id),             -- 职位ID
    user_id BIGINT REFERENCES users(id),           -- 申请人ID
    resume_id BIGINT REFERENCES resumes(id),       -- 使用的简历ID
    cover_letter TEXT,                             -- 求职信
    expected_salary INTEGER,                       -- 期望薪资（元）
    status VARCHAR(20) DEFAULT 'applied',          -- 申请状态：applied已申请/viewed已查看/interviewed已面试/offered已录用/rejected已拒绝
    hr_feedback TEXT,                             -- HR反馈
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 申请时间
    viewed_at TIMESTAMP,                          -- 查看时间
    replied_at TIMESTAMP                          -- 回复时间
);
COMMENT ON TABLE job_applications IS '求职申请表';
COMMENT ON COLUMN job_applications.status IS '申请状态：applied已申请/viewed已查看/interviewed已面试/offered已录用/rejected已拒绝';

-- 职位收藏表
CREATE TABLE job_favorites (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),    -- 用户ID
    job_id BIGINT REFERENCES jobs(id),      -- 职位ID
    notes TEXT,                             -- 收藏备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, job_id)
);
COMMENT ON TABLE job_favorites IS '职位收藏表';

-- 求职意向表
CREATE TABLE job_intentions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),    -- 用户ID
    intended_positions TEXT[],              -- 意向职位数组
    intended_industries TEXT[],             -- 意向行业数组
    intended_locations TEXT[],              -- 意向地点数组
    expected_salary_min INTEGER,            -- 期望薪资最小值
    expected_salary_max INTEGER,            -- 期望薪资最大值
    job_type VARCHAR(20),                   -- 工作类型：full_time/part_time/internship
    is_active BOOLEAN DEFAULT TRUE,         -- 是否激活求职状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);
COMMENT ON TABLE job_intentions IS '求职意向表';
```

#### 4. 房产相关表

```sql
-- 房源主表
CREATE TABLE houses (
    id BIGSERIAL PRIMARY KEY,                          -- 房源ID
    publisher_id BIGINT REFERENCES users(id),          -- 发布者ID
    category VARCHAR(20) NOT NULL,                     -- 房产分类：rent租房/second_hand二手房/new_house新房/commercial商业地产
    title VARCHAR(300) NOT NULL,                       -- 房源标题
    description TEXT,                                   -- 房源描述
    images TEXT[],                                      -- 房源图片URL数组
    price DECIMAL(12,2) NOT NULL,                      -- 价格（租金或售价，单位：元）
    unit_price DECIMAL(10,2),                          -- 单价（元/平米）
    area DECIMAL(8,2),                                 -- 建筑面积（平米）
    layout VARCHAR(50),                                -- 户型（如"3室2厅2卫"）
    rooms INTEGER,                                     -- 房间数
    halls INTEGER,                                     -- 厅数
    bathrooms INTEGER,                                 -- 卫生间数
    floor INTEGER,                                     -- 楼层
    total_floors INTEGER,                              -- 总楼层
    direction VARCHAR(20),                             -- 朝向：南/北/东/西/东南/西南等
    decoration VARCHAR(20),                            -- 装修情况：毛坯/简装/精装/豪装
    build_year INTEGER,                                -- 建筑年份
    city VARCHAR(50),                                  -- 城市
    district VARCHAR(50),                              -- 区县
    community_name VARCHAR(200),                       -- 小区名称
    address TEXT,                                      -- 详细地址
    longitude DECIMAL(10,7),                           -- 经度
    latitude DECIMAL(10,7),                            -- 纬度
    facilities TEXT[],                                 -- 房屋设施数组：空调/洗衣机/冰箱等
    tags TEXT[],                                       -- 房源标签数组：地铁房/学区房/急租等
    contact_person VARCHAR(50),                        -- 联系人
    contact_phone VARCHAR(20),                         -- 联系电话
    source_type VARCHAR(20) DEFAULT 'personal',       -- 来源类型：personal个人/agent中介/developer开发商
    is_urgent BOOLEAN DEFAULT FALSE,                   -- 是否急租/急售
    status VARCHAR(20) DEFAULT 'active',               -- 状态：active在售/inactive下架/rented已租出/sold已售出
    views INTEGER DEFAULT 0,                           -- 浏览量
    favorites INTEGER DEFAULT 0,                       -- 收藏数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP     -- 更新时间
);
COMMENT ON TABLE houses IS '房源主表';
COMMENT ON COLUMN houses.category IS '房产分类：rent租房/second_hand二手房/new_house新房/commercial商业地产';
COMMENT ON COLUMN houses.price IS '价格，租金或售价，单位：元';
COMMENT ON COLUMN houses.area IS '建筑面积，单位：平米';
COMMENT ON COLUMN houses.source_type IS '来源类型：personal个人/agent中介/developer开发商';
COMMENT ON COLUMN houses.status IS '状态：active在售/inactive下架/rented已租出/sold已售出';

-- 租房详细信息表
CREATE TABLE rent_house_details (
    house_id BIGINT PRIMARY KEY REFERENCES houses(id),    -- 关联房源ID
    rent_type VARCHAR(20) NOT NULL,                       -- 租赁类型：整租/合租
    payment_method VARCHAR(50),                           -- 付款方式：押一付三/押二付三/押三付三等
    deposit DECIMAL(10,2),                                -- 押金金额（元）
    monthly_rent DECIMAL(10,2),                           -- 月租金（元）
    utilities_included BOOLEAN DEFAULT FALSE,             -- 是否包含水电费
    min_lease_term INTEGER DEFAULT 6,                    -- 最短租期（月）
    available_date DATE,                                  -- 可入住日期
    gender_requirement VARCHAR(20) DEFAULT 'unlimited',   -- 性别要求：male男性/female女性/unlimited不限
    furniture_list TEXT[],                                -- 家具清单数组
    appliances_list TEXT[],                               -- 家电清单数组
    house_rules TEXT[],                                   -- 房屋规则数组
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP       -- 创建时间
);
COMMENT ON TABLE rent_house_details IS '租房详细信息表';
COMMENT ON COLUMN rent_house_details.rent_type IS '租赁类型：整租/合租';
COMMENT ON COLUMN rent_house_details.payment_method IS '付款方式：押一付三/押二付三/押三付三等';
COMMENT ON COLUMN rent_house_details.min_lease_term IS '最短租期，单位：月';

-- 房源收藏表
CREATE TABLE house_favorites (
    id BIGSERIAL PRIMARY KEY,                           -- 收藏ID
    user_id BIGINT REFERENCES users(id),               -- 用户ID
    house_id BIGINT REFERENCES houses(id),             -- 房源ID
    notes TEXT,                                         -- 收藏备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    -- 收藏时间
    UNIQUE(user_id, house_id)                          -- 联合唯一索引，防止重复收藏
);
COMMENT ON TABLE house_favorites IS '房源收藏表';

-- 房价历史表
CREATE TABLE house_price_history (
    id BIGSERIAL PRIMARY KEY,
    house_id BIGINT REFERENCES houses(id),  -- 房源ID
    price DECIMAL(12,2) NOT NULL,          -- 历史价格
    unit_price DECIMAL(10,2),              -- 历史单价
    price_change_rate DECIMAL(5,2),        -- 价格变化率(%)
    change_reason VARCHAR(100),            -- 变价原因
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 记录时间
);
COMMENT ON TABLE house_price_history IS '房价历史表';

-- 房源对比表
CREATE TABLE house_comparisons (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),    -- 用户ID
    house_ids BIGINT[],                     -- 对比房源ID数组
    comparison_name VARCHAR(200),           -- 对比名称
    notes TEXT,                             -- 对比备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE house_comparisons IS '房源对比表';

-- 看房预约表
CREATE TABLE house_viewings (
    id BIGSERIAL PRIMARY KEY,
    house_id BIGINT REFERENCES houses(id),  -- 房源ID
    user_id BIGINT REFERENCES users(id),    -- 预约用户ID
    viewing_date DATE NOT NULL,             -- 看房日期
    viewing_time TIME NOT NULL,             -- 看房时间
    contact_phone VARCHAR(20),              -- 联系电话
    message TEXT,                           -- 预约留言
    status VARCHAR(20) DEFAULT 'pending',   -- 状态：pending待确认/confirmed已确认/completed已完成/cancelled已取消
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE house_viewings IS '看房预约表';
```

#### 5. 社区交友相关表

```sql
-- 动态/帖子表
CREATE TABLE posts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),    -- 发布者ID
    content TEXT NOT NULL,                  -- 内容
    images TEXT[],                          -- 图片URL数组
    video_url VARCHAR(500),                 -- 视频URL
    location VARCHAR(200),                  -- 发布地点
    longitude DECIMAL(10,7),                -- 经度
    latitude DECIMAL(10,7),                 -- 纬度
    topic_ids INTEGER[],                    -- 话题ID数组
    is_dating_post BOOLEAN DEFAULT FALSE,   -- 是否交友动态
    visibility VARCHAR(20) DEFAULT 'public', -- 可见性：public公开/friends好友/private私密
    status VARCHAR(20) DEFAULT 'published', -- 状态：published已发布/hidden已隐藏/deleted已删除
    likes_count INTEGER DEFAULT 0,         -- 点赞数
    comments_count INTEGER DEFAULT 0,      -- 评论数
    shares_count INTEGER DEFAULT 0,        -- 分享数
    views_count INTEGER DEFAULT 0,         -- 浏览数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE posts IS '动态/帖子表';
COMMENT ON COLUMN posts.visibility IS '可见性：public公开/friends好友/private私密';

-- 话题表
CREATE TABLE topics (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,      -- 话题名称
    description TEXT,                       -- 话题描述
    icon VARCHAR(200),                      -- 话题图标
    background_image VARCHAR(500),          -- 背景图
    posts_count INTEGER DEFAULT 0,         -- 帖子数量
    followers_count INTEGER DEFAULT 0,     -- 关注数量
    is_hot BOOLEAN DEFAULT FALSE,          -- 是否热门话题
    is_official BOOLEAN DEFAULT FALSE,     -- 是否官方话题
    created_by BIGINT REFERENCES users(id), -- 创建者ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE topics IS '话题表';

-- 点赞表
CREATE TABLE likes (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),    -- 点赞用户ID
    target_type VARCHAR(20) NOT NULL,       -- 目标类型：post帖子/comment评论
    target_id BIGINT NOT NULL,              -- 目标ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, target_type, target_id)
);
COMMENT ON TABLE likes IS '点赞表';
COMMENT ON COLUMN likes.target_type IS '目标类型：post帖子/comment评论';

-- 评论表
CREATE TABLE comments (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),    -- 评论用户ID
    post_id BIGINT REFERENCES posts(id),    -- 帖子ID
    parent_id BIGINT REFERENCES comments(id), -- 父评论ID（回复时使用）
    content TEXT NOT NULL,                  -- 评论内容
    images TEXT[],                          -- 评论图片
    likes_count INTEGER DEFAULT 0,         -- 点赞数
    replies_count INTEGER DEFAULT 0,       -- 回复数
    is_deleted BOOLEAN DEFAULT FALSE,      -- 是否删除
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE comments IS '评论表';

-- 匹配记录表
CREATE TABLE dating_matches (
    id BIGSERIAL PRIMARY KEY,
    user1_id BIGINT REFERENCES users(id),   -- 用户1 ID
    user2_id BIGINT REFERENCES users(id),   -- 用户2 ID
    match_type VARCHAR(20) DEFAULT 'system', -- 匹配类型：system系统推荐/mutual互相喜欢
    compatibility_score INTEGER,            -- 匹配度分数（0-100）
    status VARCHAR(20) DEFAULT 'matched',   -- 状态：matched已匹配/chatting聊天中/blocked已屏蔽
    matched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_interaction_at TIMESTAMP,         -- 最后互动时间
    UNIQUE(user1_id, user2_id)
);
COMMENT ON TABLE dating_matches IS '匹配记录表';
COMMENT ON COLUMN dating_matches.match_type IS '匹配类型：system系统推荐/mutual互相喜欢';
```

#### 6. 零工相关表

```sql
-- 零工任务表
CREATE TABLE gig_tasks (
    id BIGSERIAL PRIMARY KEY,                         -- 任务ID
    publisher_id BIGINT REFERENCES users(id),        -- 发布者ID
    title VARCHAR(300) NOT NULL,                     -- 任务标题
    category VARCHAR(50),                             -- 任务分类：餐饮服务/保洁清洁/搬运装卸/配送跑腿等
    description TEXT,                                 -- 任务描述
    location VARCHAR(200),                            -- 工作地点
    address TEXT,                                     -- 详细地址
    start_time TIMESTAMP,                             -- 开始时间
    end_time TIMESTAMP,                               -- 结束时间
    hourly_rate DECIMAL(8,2),                        -- 时薪（元/小时）
    total_amount DECIMAL(10,2),                       -- 总金额（元）
    max_applicants INTEGER DEFAULT 1,                -- 最大申请人数
    requirements TEXT[],                              -- 任务要求数组：健康证/有经验/身强力壮等
    tags TEXT[],                                      -- 任务标签数组：日结/包吃/轻松等
    gender_required VARCHAR(20) DEFAULT 'unlimited', -- 性别要求：male男性/female女性/unlimited不限
    age_min INTEGER,                                  -- 最小年龄要求
    age_max INTEGER,                                  -- 最大年龄要求
    health_certificate BOOLEAN DEFAULT FALSE,         -- 是否需要健康证
    contact_person VARCHAR(50),                       -- 联系人
    contact_phone VARCHAR(20),                        -- 联系电话
    status VARCHAR(20) DEFAULT 'published',           -- 状态：published已发布/in_progress进行中/completed已完成/cancelled已取消
    settlement_method VARCHAR(20),                    -- 结算方式：daily日结/weekly周结/completion完工结算
    is_urgent BOOLEAN DEFAULT FALSE,                  -- 是否紧急
    views INTEGER DEFAULT 0,                          -- 浏览量
    applications INTEGER DEFAULT 0,                   -- 申请人数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP    -- 更新时间
);
COMMENT ON TABLE gig_tasks IS '零工任务表';
COMMENT ON COLUMN gig_tasks.hourly_rate IS '时薪，单位：元/小时';
COMMENT ON COLUMN gig_tasks.total_amount IS '总金额，单位：元';
COMMENT ON COLUMN gig_tasks.settlement_method IS '结算方式：daily日结/weekly周结/completion完工结算';
COMMENT ON COLUMN gig_tasks.status IS '状态：published已发布/in_progress进行中/completed已完成/cancelled已取消';

-- 零工申请表
CREATE TABLE gig_applications (
    id BIGSERIAL PRIMARY KEY,                         -- 申请ID
    task_id BIGINT REFERENCES gig_tasks(id),         -- 任务ID
    user_id BIGINT REFERENCES users(id),             -- 申请人ID
    message TEXT,                                     -- 申请留言
    contact_info VARCHAR(100),                        -- 联系方式
    experience_description TEXT,                      -- 相关经验描述
    status VARCHAR(20) DEFAULT 'applied',             -- 申请状态：applied已申请/accepted已接受/rejected已拒绝/completed已完成
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 申请时间
    reviewed_at TIMESTAMP,                            -- 审核时间
    start_work_at TIMESTAMP,                          -- 开始工作时间
    complete_at TIMESTAMP,                            -- 完成时间
    rating INTEGER,                                   -- 评分（1-5分）
    feedback TEXT,                                    -- 反馈评价
    UNIQUE(task_id, user_id)                         -- 确保不重复申请
);
COMMENT ON TABLE gig_applications IS '零工申请表';
COMMENT ON COLUMN gig_applications.status IS '申请状态：applied已申请/accepted已接受/rejected已拒绝/completed已完成';
COMMENT ON COLUMN gig_applications.rating IS '评分，1-5分';
```

#### 7. 消息相关表

```sql
-- 会话表
CREATE TABLE conversations (
    id BIGSERIAL PRIMARY KEY,                        -- 会话ID
    type VARCHAR(20) DEFAULT 'private',              -- 会话类型：private私聊/group群聊
    participants BIGINT[],                           -- 参与者ID数组
    title VARCHAR(200),                              -- 会话标题（群聊时使用）
    avatar VARCHAR(500),                             -- 会话头像（群聊时使用）
    last_message_id BIGINT,                          -- 最后一条消息ID
    last_message_time TIMESTAMP,                     -- 最后消息时间
    is_muted BOOLEAN DEFAULT FALSE,                  -- 是否静音
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP   -- 更新时间
);
COMMENT ON TABLE conversations IS '会话表';
COMMENT ON COLUMN conversations.type IS '会话类型：private私聊/group群聊';

-- 消息表
CREATE TABLE messages (
    id BIGSERIAL PRIMARY KEY,                        -- 消息ID
    conversation_id BIGINT REFERENCES conversations(id), -- 会话ID
    sender_id BIGINT REFERENCES users(id),          -- 发送者ID
    message_type VARCHAR(20) DEFAULT 'text',         -- 消息类型：text文本/image图片/voice语音/video视频/file文件/location位置
    content TEXT,                                    -- 消息内容
    extra_data JSONB,                                -- 额外数据（图片尺寸、语音时长、文件大小等）
    reply_to_message_id BIGINT REFERENCES messages(id), -- 回复的消息ID
    status VARCHAR(20) DEFAULT 'sent',               -- 消息状态：sent已发送/delivered已送达/read已读
    is_recalled BOOLEAN DEFAULT FALSE,               -- 是否已撤回
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP   -- 创建时间
);
COMMENT ON TABLE messages IS '消息表';
COMMENT ON COLUMN messages.message_type IS '消息类型：text文本/image图片/voice语音/video视频/file文件/location位置';
COMMENT ON COLUMN messages.status IS '消息状态：sent已发送/delivered已送达/read已读';

-- 消息已读状态表
CREATE TABLE message_reads (
    id BIGSERIAL PRIMARY KEY,                       -- 已读记录ID
    message_id BIGINT REFERENCES messages(id),     -- 消息ID
    user_id BIGINT REFERENCES users(id),           -- 用户ID
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    -- 已读时间
    UNIQUE(message_id, user_id)                    -- 联合唯一索引
);
COMMENT ON TABLE message_reads IS '消息已读状态表';
```

#### 8. 系统相关表

```sql
-- 系统通知表
CREATE TABLE system_notifications (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,           -- 通知标题
    content TEXT NOT NULL,                 -- 通知内容
    type VARCHAR(20) NOT NULL,             -- 通知类型：system系统/promotion活动/update更新
    target_type VARCHAR(20) DEFAULT 'all', -- 目标类型：all全部/user指定用户/role指定角色
    target_users BIGINT[],                 -- 目标用户ID数组
    image_url VARCHAR(500),                -- 通知图片
    action_url VARCHAR(500),               -- 点击跳转链接
    is_published BOOLEAN DEFAULT FALSE,    -- 是否发布
    publish_at TIMESTAMP,                  -- 发布时间
    expire_at TIMESTAMP,                   -- 过期时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE system_notifications IS '系统通知表';

-- 用户通知状态表
CREATE TABLE user_notification_status (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),   -- 用户ID
    notification_id BIGINT REFERENCES system_notifications(id), -- 通知ID
    is_read BOOLEAN DEFAULT FALSE,         -- 是否已读
    read_at TIMESTAMP,                     -- 已读时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, notification_id)
);
COMMENT ON TABLE user_notification_status IS '用户通知状态表';

-- VIP套餐表
CREATE TABLE vip_packages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,            -- 套餐名称
    description TEXT,                      -- 套餐描述
    duration_days INTEGER NOT NULL,       -- 有效期（天）
    original_price DECIMAL(8,2) NOT NULL, -- 原价
    current_price DECIMAL(8,2) NOT NULL,  -- 现价
    features TEXT[],                       -- 功能特权数组
    is_active BOOLEAN DEFAULT TRUE,       -- 是否有效
    sort_order INTEGER DEFAULT 0,         -- 排序
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE vip_packages IS 'VIP套餐表';

-- 订单表
CREATE TABLE orders (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(32) UNIQUE NOT NULL,  -- 订单号
    user_id BIGINT REFERENCES users(id),   -- 用户ID
    type VARCHAR(20) NOT NULL,             -- 订单类型：vip/recharge/service
    product_id BIGINT,                     -- 产品ID（VIP套餐ID等）
    product_name VARCHAR(200),             -- 产品名称
    amount DECIMAL(10,2) NOT NULL,         -- 订单金额
    payment_method VARCHAR(20),            -- 支付方式：wechat/alipay/balance
    payment_status VARCHAR(20) DEFAULT 'pending', -- 支付状态：pending待支付/paid已支付/failed失败/refunded已退款
    trade_no VARCHAR(64),                  -- 第三方交易号
    paid_at TIMESTAMP,                     -- 支付时间
    expired_at TIMESTAMP,                  -- 过期时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE orders IS '订单表';
COMMENT ON COLUMN orders.type IS '订单类型：vip会员/recharge充值/service服务';

-- 搜索历史表
CREATE TABLE search_history (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),   -- 用户ID（可为空，游客搜索）
    search_type VARCHAR(20) NOT NULL,      -- 搜索类型：job职位/house房源/gig零工/user用户
    keyword VARCHAR(200) NOT NULL,         -- 搜索关键词
    search_count INTEGER DEFAULT 1,        -- 搜索次数
    last_search_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE search_history IS '搜索历史表';
```

## API接口设计

### 认证相关接口
```http
POST   /api/v1/auth/login          # 用户登录
POST   /api/v1/auth/register       # 用户注册  
POST   /api/v1/auth/logout         # 用户登出
POST   /api/v1/auth/refresh        # 刷新Token
POST   /api/v1/auth/send-sms       # 发送短信验证码
POST   /api/v1/auth/verify-sms     # 验证短信验证码
POST   /api/v1/auth/wechat-login   # 微信登录
```

### 用户相关接口
```http
GET    /api/v1/users/profile       # 获取用户信息
PUT    /api/v1/users/profile       # 更新用户信息
POST   /api/v1/users/avatar        # 上传头像
GET    /api/v1/users/stats         # 用户统计信息
POST   /api/v1/users/verify        # 实名认证
GET    /api/v1/users/balance       # 获取账户余额
POST   /api/v1/users/recharge      # 账户充值
```

### 招聘相关接口
```http
GET    /api/v1/jobs                # 获取职位列表（支持筛选）
GET    /api/v1/jobs/:id            # 获取职位详情
POST   /api/v1/jobs                # 发布职位
PUT    /api/v1/jobs/:id            # 更新职位
DELETE /api/v1/jobs/:id            # 删除职位
POST   /api/v1/jobs/:id/apply      # 申请职位
GET    /api/v1/jobs/applications   # 获取申请记录
PUT    /api/v1/jobs/applications/:id # 更新申请状态

GET    /api/v1/resumes             # 获取简历列表
GET    /api/v1/resumes/:id         # 获取简历详情
POST   /api/v1/resumes             # 创建简历
PUT    /api/v1/resumes/:id         # 更新简历
DELETE /api/v1/resumes/:id         # 删除简历

GET    /api/v1/companies           # 获取企业列表
POST   /api/v1/companies           # 企业认证申请
PUT    /api/v1/companies/:id       # 更新企业信息
```

### 房产相关接口
```http
GET    /api/v1/houses              # 获取房源列表（支持筛选）
GET    /api/v1/houses/:id          # 获取房源详情
POST   /api/v1/houses              # 发布房源
PUT    /api/v1/houses/:id          # 更新房源
DELETE /api/v1/houses/:id          # 删除房源
POST   /api/v1/houses/:id/favorite # 收藏/取消收藏房源
GET    /api/v1/houses/favorites    # 获取收藏列表
POST   /api/v1/houses/:id/inquiry  # 房源咨询
GET    /api/v1/houses/nearby       # 获取附近房源
```

### 零工相关接口
```http
GET    /api/v1/gigs                # 获取零工列表（支持筛选）
GET    /api/v1/gigs/:id            # 获取零工详情
POST   /api/v1/gigs                # 发布零工
PUT    /api/v1/gigs/:id            # 更新零工
DELETE /api/v1/gigs/:id            # 删除零工
POST   /api/v1/gigs/:id/apply      # 申请零工
GET    /api/v1/gigs/applications   # 获取申请记录
PUT    /api/v1/gigs/applications/:id # 更新申请状态
POST   /api/v1/gigs/applications/:id/rate # 评价
```

### 消息相关接口
```http
GET    /api/v1/conversations       # 获取会话列表
GET    /api/v1/conversations/:id   # 获取会话详情
POST   /api/v1/conversations       # 创建会话
DELETE /api/v1/conversations/:id   # 删除会话

GET    /api/v1/conversations/:id/messages # 获取消息列表
POST   /api/v1/messages            # 发送消息
PUT    /api/v1/messages/:id/read   # 标记消息已读
DELETE /api/v1/messages/:id        # 撤回消息

WebSocket /ws/chat                 # WebSocket连接
```

### 通用接口
```http
POST   /api/v1/upload/image        # 上传图片
POST   /api/v1/upload/file         # 上传文件
GET    /api/v1/regions             # 获取地区列表
GET    /api/v1/categories          # 获取分类列表
POST   /api/v1/feedback            # 意见反馈
GET    /api/v1/version             # 获取版本信息
```

## 部署和运维

### Docker容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  api-server:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=***************************************/fnbdb
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
      
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: fnbdb
      POSTGRES_USER: fnbdb  
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/migration/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
      
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./configs/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api-server
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 监控和日志
- **应用监控**: Prometheus + Grafana
- **日志收集**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **错误监控**: Sentry
- **性能监控**: New Relic 或 DataDog
- **健康检查**: 定期检查服务状态和数据库连接

### 备份策略
- **数据库备份**: 每日全量备份 + 实时增量备份
- **文件备份**: 图片和文档定期同步到云存储
- **配置备份**: 配置文件版本控制管理
- **恢复测试**: 定期进行灾难恢复演练

## 技术选型说明

### 为什么选择 Go + Gin
- **高性能**: Go的并发特性和Gin的轻量级设计
- **简洁性**: 语法简单，开发效率高
- **生态丰富**: 丰富的第三方库支持
- **部署便利**: 单一可执行文件，容器化友好

### 为什么选择 PostgreSQL
- **功能强大**: 支持复杂查询、JSON、全文搜索
- **地理支持**: 内置地理位置查询功能
- **扩展性**: 良好的扩展性和性能
- **开源稳定**: 成熟的开源数据库

### 为什么选择 Redis
- **高性能**: 内存存储，响应速度快
- **数据结构**: 支持多种数据结构
- **功能丰富**: 缓存、分布式锁、排行榜
- **集群支持**: 良好的集群和持久化支持

### 为什么选择 Centrifugo
- **实时通信**: 专业的实时通信解决方案
- **高性能**: 支持大量并发连接
- **多协议**: 支持WebSocket、HTTP等多种协议
- **易集成**: 提供Go客户端，集成简单

---

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护人员**: 开发团队
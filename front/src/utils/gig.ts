import { GIG_SALARY_UNIT_MAP } from '@/constants/gig';
import type { Gig } from '@/types/gig';

/**
 * 格式化薪资显示
 * @param salary 薪资（分）
 * @param salary_unit 薪资单位
 */
export function formatSalary(salary: number, salary_unit: string): string {
    const yuan = (salary / 100).toFixed(2);
    const unit = GIG_SALARY_UNIT_MAP[salary_unit] || '次';
    return `¥${yuan}/${unit}`;
}

/**
 * 格式化距离显示
 * @param distance 距离（米）
 */
export function formatDistance(distance?: number): string {
    if (!distance) return '';

    if (distance < 1000) {
        return `${distance}m`;
    } else {
        return `${(distance / 1000).toFixed(1)}km`;
    }
}

/**
 * 格式化工作时长
 * @param start_time 开始时间
 * @param end_time 结束时间
 */
export function formatWorkDuration(start_time: string, end_time: string): string {
    const start = new Date(start_time);
    const end = new Date(end_time);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = Math.round(diffMs / (1000 * 60 * 60));

    if (diffHours < 24) {
        return `${diffHours}小时`;
    } else {
        const days = Math.floor(diffHours / 24);
        const remainingHours = diffHours % 24;
        if (remainingHours === 0) {
            return `${days}天`;
        } else {
            return `${days}天${remainingHours}小时`;
        }
    }
}

/**
 * 获取申请状态文本
 * @param status 状态值
 */
export function getStatusText(status: string): string {
    const statusMap: Record<string, string> = {
        pending: '待处理',
        confirmed: '已确认',
        rejected: '已拒绝',
        withdrawn: '已撤回',
        cancelled: '已取消',
        completed: '已完成'
    };

    return statusMap[status] || status;
}

/**
 * 获取申请状态对应的样式类名
 * @param status 状态值
 */
export function getStatusClass(status: string): string {
    const statusClassMap: Record<string, string> = {
        pending: 'text-warning',
        confirmed: 'text-success',
        rejected: 'text-error',
        withdrawn: 'text-gray',
        cancelled: 'text-gray',
        completed: 'text-primary'
    };

    return statusClassMap[status] || 'text-gray';
}

/**
 * 计算零工的紧急程度
 * @param gig 零工信息
 */
export function getUrgencyLevel(gig: Gig): 'high' | 'medium' | 'low' {
    const now = new Date();
    const startTime = new Date(gig.start_time);
    const diffHours = (startTime.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (diffHours <= 24) return 'high';
    if (diffHours <= 72) return 'medium';
    return 'low';
}

/**
 * 检查零工是否即将开始（24小时内）
 * @param gig 零工信息
 */
export function isGigStartingSoon(gig: Gig): boolean {
    return getUrgencyLevel(gig) === 'high';
} 
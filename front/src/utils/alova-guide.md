# Alova 使用指南

本项目已集成 Alova 作为请求库，提供了高性能的数据获取和状态管理功能。

## 基本配置

Alova 实例已在 `@/utils/alova.ts` 中配置完成，包含：
- uni-app 适配器
- Vue3 状态钩子
- 请求/响应拦截器
- 自动 token 管理
- 缓存配置
- 错误处理

## 在组件中使用

### 1. 基本 GET 请求

```vue
<template>
  <div v-if="loading">加载中...</div>
  <div v-else>{{ data }}</div>
</template>

<script setup>
import { useRequest } from 'alova'
import { api } from '@/api'

// 立即发送请求
const { data, loading, error } = useRequest(() => api.get('/users'))
</script>
```

### 2. 手动触发请求

```vue
<script setup>
import { useRequest } from 'alova'
import { api } from '@/api'

// 不立即发送请求
const { data, loading, send } = useRequest(
  (id) => api.get(`/users/${id}`),
  { immediate: false }
)

// 手动触发
const handleClick = () => {
  send(123) // 传入参数
}
</script>
```

### 3. 监听式请求 (useWatcher)

```vue
<script setup>
import { useWatcher } from 'alova'
import { api } from '@/api'

const searchKey = ref('')

// 监听 searchKey 变化，自动发送请求
const { data, loading } = useWatcher(
  () => api.get('/search', { q: searchKey.value }),
  [searchKey], // 监听的变量
  { immediate: false } // 初始不执行
)
</script>
```

### 4. POST 请求

```vue
<script setup>
import { useRequest } from 'alova'
import { api } from '@/api'

const { loading, send: submitForm } = useRequest(
  (formData) => api.post('/users', formData),
  { immediate: false }
)

const handleSubmit = (data) => {
  submitForm(data)
}
</script>
```

### 5. 文件上传

```vue
<script setup>
import { useRequest } from 'alova'
import { api } from '@/api'

const { loading, uploading, send: upload } = useRequest(
  (file) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.upload('/upload', formData)
  },
  { immediate: false }
)

const handleFileSelect = () => {
  uni.chooseImage({
    success: (res) => {
      upload(res.tempFilePaths[0])
    }
  })
}
</script>
```

### 6. 分页加载

```vue
<script setup>
import { usePagination } from 'alova'
import { api } from '@/api'

const {
  data: list,
  loading,
  hasMore,
  loadMore
} = usePagination(
  (page, pageSize) => api.get('/posts', { page, pageSize }),
  {
    append: true, // 追加模式
    initialPageSize: 10
  }
)
</script>
```

## 高级用法

### 请求策略

```javascript
// 静默请求策略
import { useSQRequest } from '@alova/scene-vue'

// 后台发送请求，不阻塞界面
const { loading, data, error } = useSQRequest(() => api.get('/background-task'))
```

### 缓存管理

```javascript
import { invalidateCache, setCache } from 'alova'

// 手动设置缓存
setCache(api.get('/users'), userData)

// 清除缓存
invalidateCache(api.get('/users'))
```

### 乐观更新

```javascript
import { useRequest } from 'alova'

const { send: updateUser } = useRequest(
  (userData) => api.put('/users/1', userData),
  {
    immediate: false,
    // 乐观更新
    optimistic: {
      response: () => userData,
      transform: (data) => data
    }
  }
)
```

## 最佳实践

1. **使用 TypeScript 类型**：为 API 返回数据定义明确的类型
2. **合理使用缓存**：GET 请求默认缓存 5 分钟，合理利用缓存提升性能
3. **错误处理**：全局错误处理已配置，局部可通过 error 状态处理
4. **请求共享**：相同请求会自动共享，避免重复发送
5. **状态管理**：利用 alova 的状态管理能力，减少组件间的状态传递

## 注意事项

- 确保在 Vue 组件的 setup 函数中使用 alova hooks
- 文件上传时需要设置正确的 `requestType: 'upload'`
- 下载文件时需要设置 `requestType: 'download'`
- 监听式请求的依赖数组要包含所有响应式变量

## 更多信息

- [Alova 官方文档](https://alova.js.org/)
- [uni-app 适配器文档](https://alova.js.org/zh-CN/resource/request-adapter/uniapp) 
<template>
  <view class="container">
    <!-- 顶部统计 -->
    <view class="summary-section">
      <view class="summary-card">
        <view class="value text-primary">{{ stats.completed }}</view>
        <view class="label text-secondary">已完成</view>
      </view>
      <view class="summary-card">
        <view class="value text-green">{{ stats.inProgress }}</view>
        <view class="label text-secondary">进行中</view>
      </view>
      <view class="summary-card">
        <view class="value text-yellow">{{ stats.recruiting }}</view>
        <view class="label text-secondary">招募中</view>
      </view>
    </view>

    <!-- 状态筛选 -->
    <view class="filter-section">
      <tui-tab
        :tabs="tabs"
        :current="activeTabIndex"
        item-width="25%"
        @change="handleTabChange"
        :scroll="true"
        :color="'var(--text-secondary)'"
        :selectedColor="'var(--primary)'"
        :backgroundColor="'transparent'"
        :selectedBackgroundColor="'var(--bg-primary-light)'"
        :borderRadius="16"
      />
    </view>

    <!-- 零工列表 -->
    <view class="gig-list">
      <view v-if="loading" class="loading-container">
        <view class="loading-text">正在加载...</view>
      </view>
      <view v-else-if="error" class="error-container">
        <view class="error-text">{{ error }}</view>
        <view class="retry-btn" @click="fetchGigs">重新加载</view>
      </view>
      <view v-else-if="filteredGigs.length === 0" class="empty-container">
        <view class="empty-text">暂无零工数据</view>
      </view>
      <view
        v-else
        v-for="gig in filteredGigs"
        :key="gig.id"
        class="card bg-card shadow-sm"
      >
        <view class="card-header">
          <text class="title text-base font-bold">{{ gig.title }}</text>
          <view
            class="status-badge"
            :class="getGigStatusDetails(gig.status).class"
          >
            <text>{{ getGigStatusDetails(gig.status).text }}</text>
          </view>
        </view>
        <view class="card-body">
          <view class="info-row">
            <view class="i-mdi-clock-outline text-info mr-1"></view>
            <text class="text-secondary">{{
              formatGigTime(gig.start_time, gig.end_time)
            }}</text>
          </view>
          <view class="info-row">
            <view class="i-mdi-map-marker-outline text-info mr-1"></view>
            <text class="text-secondary">{{ gig.address_name }}</text>
          </view>
          <view class="tag-container">
            <text v-for="tag in gig.tags" :key="tag" class="tag tag-sm">{{
              tag
            }}</text>
          </view>
          <view class="salary-enrollment">
            <text class="salary text-red font-bold">
              ¥{{ gig.salary / 100 }}/{{ getSalaryUnit(gig.salary_unit) }}
            </text>
            <text class="enrollment text-info">
              已报名 {{ gig.current_people_count }}/{{ gig.people_count }} 人
            </text>
          </view>
        </view>
        <!-- 操作按钮 -->
        <view class="card-footer">
          <view
            v-for="button in getActionButtons(gig)"
            :key="button.text"
            class="action-btn"
            :class="button.type"
            @click="handleAction(button.action, gig)"
          >
            {{ button.text }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { listMyGigs, deleteGig as deleteGigAPI } from "@/api/gig";
import { GigStatus, getGigStatusDetails } from "@/constants/gig";
import dayjs from "dayjs";
import type { Gig } from "@/types/gig";

// 定义状态和数据
const activeTab = ref<string>("all");
const loading = ref(false);
const allGigs = ref<Gig[]>([]);
const error = ref<string | null>(null);

// 筛选标签
const tabs = [
  { key: "all", name: "全部" },
  { key: GigStatus.Recruiting, name: "招募中" },
  { key: GigStatus.InProgress, name: "进行中" },
  { key: GigStatus.Completed, name: "已完成" },
  { key: GigStatus.Paused, name: "暂停中" },
  { key: GigStatus.Closed, name: "已关闭" },
];

const activeTabIndex = computed(() =>
  tabs.findIndex((t) => t.key === activeTab.value)
);

const handleTabChange = (e: { index: number }) => {
  activeTab.value = tabs[e.index].key;
};

// 根据当前标签过滤零工
const filteredGigs = computed(() => {
  if (activeTab.value === "all") {
    return allGigs.value;
  }
  return allGigs.value.filter((gig) => gig.status === activeTab.value);
});

// 统计数据
const stats = computed(() => {
  return {
    completed: allGigs.value.filter((g) => g.status === GigStatus.Completed)
      .length,
    inProgress: allGigs.value.filter((g) => g.status === GigStatus.InProgress)
      .length,
    recruiting: allGigs.value.filter((g) => g.status === GigStatus.Recruiting)
      .length,
  };
});

// 格式化时间
const formatGigTime = (startTime: string, endTime: string) => {
  const start = dayjs(startTime);
  const end = dayjs(endTime);
  return `${start.format("MM月DD日 HH:mm")} - ${end.format("HH:mm")}`;
};

// 获取薪资单位
const getSalaryUnit = (unit: number) => {
  const unitMap: Record<number, string> = {
    1: "小时",
    2: "天",
    3: "件",
    4: "总价",
  };
  return unitMap[unit] || "次";
};

// 根据状态获取操作按钮
const getActionButtons = (gig: Gig) => {
  switch (gig.status) {
    case GigStatus.Draft:
      return [
        { text: "发布", type: "primary", action: "publish" },
        { text: "编辑", type: "secondary", action: "edit" },
        { text: "删除", type: "danger", action: "delete" },
      ];
    case GigStatus.Recruiting:
      return [
        { text: "查看报名", type: "primary", action: "viewApplicants" },
        { text: "暂停招聘", type: "warning", action: "pause" },
        { text: "编辑", type: "secondary", action: "edit" },
      ];
    case GigStatus.Paused:
      return [
        { text: "继续招聘", type: "success", action: "resume" },
        { text: "关闭", type: "danger", action: "close" },
      ];
    case GigStatus.Locked:
    case GigStatus.InProgress:
      return [
        { text: "查看报名", type: "primary", action: "viewApplicants" },
        { text: "联系工人", type: "success", action: "contact" },
      ];
    case GigStatus.Completed:
    case GigStatus.Closed:
      return [
        { text: "删除", type: "danger", action: "delete" },
        { text: "再次发布", type: "secondary", action: "republish" },
      ];
    default:
      return [{ text: "查看详情", type: "primary", action: "viewDetail" }];
  }
};

const fetchGigs = async () => {
  loading.value = true;
  error.value = null;
  try {
    const response = await listMyGigs({ page: 1, page_size: 100 });
    const responseData = (response as any).data || response;
    allGigs.value = responseData.list || [];
  } catch (err: any) {
    console.error("Failed to fetch gigs:", err);
    error.value = err.message || "加载数据失败，请稍后重试。";
  } finally {
    loading.value = false;
  }
};

const handleAction = (action: string, gig: Gig) => {
  console.log(`Action: ${action}, Gig ID: ${gig.id}`);
  switch (action) {
    case "viewApplicants":
      uni.navigateTo({ url: `/pages/gig/applicants?gigId=${gig.id}` });
      break;
    case "edit":
      uni.navigateTo({ url: `/pages/gig/publish?id=${gig.id}` });
      break;
    case "delete":
      uni.showModal({
        title: "确认删除",
        content: "删除后无法恢复，确定要删除吗？",
        success: async (res) => {
          if (res.confirm) {
            await deleteGigAPI(gig.id);
            fetchGigs(); // 重新加载列表
          }
        },
      });
      break;
    case "republish":
      // 跳转到发布页，并携带参数
      uni.navigateTo({ url: `/pages/gig/publish?republish=true&id=${gig.id}` });
      break;
    // TODO: 实现其他操作的逻辑
    case "pause":
    case "resume":
    case "close":
    case "contact":
    case "publish":
    case "viewDetail":
      uni.showToast({ title: `操作“${action}”待实现`, icon: "none" });
      break;
  }
};

onMounted(() => {
  fetchGigs();
});
</script>

<style scoped>
.container {
  padding: 24rpx;
  background-color: var(--bg-color);
}

.summary-section {
  display: flex;
  justify-content: space-around;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: var(--card-bg);
  border-radius: 16rpx;
}

.summary-card {
  text-align: center;
}

.summary-card .value {
  font-size: 40rpx;
  font-weight: bold;
}

.summary-card .label {
  font-size: 24rpx;
  margin-top: 8rpx;
}

.filter-section {
  margin-bottom: 24rpx;
}

.gig-list {
  padding-bottom: 40rpx;
}

.card {
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  background-color: var(--card-bg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.title {
  font-size: 32rpx;
}

.status-badge {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #fff;
}
.status-recruiting {
  background-color: var(--green);
}
.status-paused {
  background-color: var(--yellow);
}
.status-locked {
  background-color: var(--blue);
}
.status-ongoing {
  background-color: var(--blue);
}
.status-completed {
  background-color: var(--text-tertiary);
}
.status-closed {
  background-color: var(--red);
}
.status-draft {
  background-color: var(--text-tertiary);
}

.card-body .info-row {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  margin-bottom: 8rpx;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 16rpx;
}

.tag {
  background-color: var(--bg-primary-light);
  color: var(--primary);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.salary-enrollment {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
  padding-top: 16rpx;
  border-top: 1px solid var(--border-color);
}

.salary {
  font-size: 36rpx;
}

.enrollment {
  font-size: 26rpx;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  margin-top: 24rpx;
  padding-top: 16rpx;
  border-top: 1px solid var(--border-color);
}

.action-btn {
  padding: 8rpx 24rpx;
  border-radius: 32rpx;
  font-size: 26rpx;
  cursor: pointer;
}
.action-btn.primary {
  background-color: var(--primary);
  color: #fff;
}
.action-btn.secondary {
  border: 1px solid var(--primary);
  color: var(--primary);
}
.action-btn.success {
  background-color: var(--green);
  color: #fff;
}
.action-btn.warning {
  background-color: var(--yellow);
  color: #fff;
}
.action-btn.danger {
  background-color: var(--red);
  color: #fff;
}
.action-btn.info {
  background-color: var(--text-tertiary);
  color: #fff;
}

.empty-container,
.loading-container,
.error-container {
  text-align: center;
  padding: 80rpx 0;
  color: var(--text-secondary);
}

.retry-btn {
  margin-top: 24rpx;
  padding: 12rpx 32rpx;
  border: 1px solid var(--primary);
  color: var(--primary);
  border-radius: 32rpx;
  display: inline-block;
}
</style>

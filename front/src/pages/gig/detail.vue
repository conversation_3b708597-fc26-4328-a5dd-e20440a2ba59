<template>
  <view v-if="gig" class="gig-detail-page">
    <scroll-view scroll-y class="detail-scroll-view">
      <!-- 核心信息 -->
      <view class="card main-info-card">
        <view class="flex justify-between items-start">
          <text class="gig-title">{{ gig.title }}</text>
          <view
            class="status-badge"
            :class="getGigStatusDetails(gig.status).class"
          >
            <text>{{ getGigStatusDetails(gig.status).text }}</text>
          </view>
        </view>
        <view class="gig-price">
          <text class="price-value">{{ (gig.salary / 100).toFixed(2) }}</text>
          <text class="price-unit"
            >元/{{ GIG_SALARY_UNIT_MAP[gig.salary_unit] }}</text
          >
        </view>
        <view class="gig-tags">
          <view v-if="gig.is_urgent" class="tag urgent-tag">紧急</view>
          <view v-for="(tag, index) in gig.tags" :key="index" class="tag">
            {{ tag }}
          </view>
        </view>
      </view>

      <!-- 详细信息 -->
      <view class="card detail-info-card">
        <view class="detail-item">
          <text class="i-carbon-calendar detail-icon"></text>
          <view>
            <text class="item-label">工作时间</text>
            <text class="item-value">{{
              formatTime(gig.start_time, gig.end_time)
            }}</text>
          </view>
        </view>
        <view class="detail-item">
          <text class="i-carbon-location detail-icon"></text>
          <view>
            <text class="item-label">工作地点</text>
            <text class="item-value">{{ gig.full_address }}</text>
          </view>
        </view>
        <view class="detail-item">
          <text class="i-carbon-group-account detail-icon"></text>
          <view>
            <text class="item-label">招聘人数</text>
            <text class="item-value">
              {{ gig.people_count }}人，已报名 {{ gig.current_people_count }}人
            </text>
          </view>
        </view>
        <view class="detail-item">
          <text class="i-carbon-settings-check detail-icon"></text>
          <view>
            <text class="item-label">审批方式</text>
            <text class="item-value">{{
              gig.approval_mode === "auto" ? "自动录用" : "手动审批"
            }}</text>
          </view>
        </view>
      </view>

      <!-- 任务描述 -->
      <view class="card description-card">
        <text class="card-title">任务描述</text>
        <text class="description-text">{{ gig.description }}</text>
        <view v-if="gig.images && gig.images.length > 0" class="image-gallery">
          <image
            v-for="(img, idx) in gig.images"
            :key="idx"
            :src="img"
            class="gallery-image"
            mode="aspectFill"
          />
        </view>
      </view>

      <!-- 职位要求 -->
      <view class="card requirement-card">
        <text class="card-title">职位要求</text>
        <view class="requirement-grid">
          <view class="req-item"
            ><text class="req-label">性别：</text
            ><text class="req-value">{{
              GIG_GENDER_MAP[gig.gender]
            }}</text></view
          >
          <view class="req-item"
            ><text class="req-label">年龄：</text
            ><text class="req-value"
              >{{ gig.age_min }}-{{ gig.age_max }}岁</text
            ></view
          >
          <view class="req-item"
            ><text class="req-label">经验：</text
            ><text class="req-value">{{
              GIG_EXPERIENCE_MAP[gig.experience]
            }}</text></view
          >
          <view class="req-item"
            ><text class="req-label">学历：</text
            ><text class="req-value">{{
              GIG_EDUCATION_MAP[gig.education]
            }}</text></view
          >
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="card contact-card">
        <text class="card-title">联系方式</text>
        <view class="contact-item">
          <text class="i-carbon-user detail-icon"></text>
          <text>{{ gig.contact_name }}</text>
        </view>
        <view class="contact-item">
          <text class="i-carbon-phone detail-icon"></text>
          <text>{{ gig.contact_phone }}</text>
        </view>
      </view>

      <!-- 雇主信息 -->
      <view class="card employer-card">
        <view class="employer-content">
          <image :src="gig.publisher.avatar" class="employer-avatar" />
          <view class="employer-details">
            <text class="employer-name">{{ gig.publisher.nickname }}</text>
          </view>
        </view>
        <text class="i-carbon-chevron-right arrow-icon"></text>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-item">
        <text class="i-carbon-star action-icon"></text>
        <text class="action-text">收藏</text>
      </view>
      <view class="action-item">
        <text class="i-carbon-chat action-icon"></text>
        <text class="action-text">沟通</text>
      </view>
      <button
        class="apply-button"
        @click="handleApply"
        :disabled="applyButton.disabled"
        :class="{ 'disabled-button': applyButton.disabled }"
      >
        {{ applyButton.text }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getGig, applyForGig } from "@/api/gig";
import { type Gig } from "@/types/gig";
import {
  GigStatus,
  getGigStatusDetails,
  GIG_SALARY_UNIT_MAP,
  GIG_GENDER_MAP,
  GIG_EDUCATION_MAP,
  GIG_EXPERIENCE_MAP,
} from "@/constants/gig";
import dayjs from "dayjs";
import { useUserStore } from "@/stores/user";

const gig = ref<Gig | null>(null);
const gigId = ref<number>(0);
const userStore = useUserStore();
// 增加一个字段，用于判断用户是否已申请
const hasApplied = ref(false);

onLoad(async (options) => {
  if (!options?.id) {
    uni.showToast({ title: "参数错误", icon: "none" });
    uni.navigateBack();
    return;
  }
  gigId.value = parseInt(options.id, 10);
  fetchGigDetail();
});

async function fetchGigDetail() {
  if (!gigId.value) return;
  uni.showLoading({ title: "加载中..." });
  try {
    const { data, message } = await getGig(gigId.value);
    gig.value = data;
  } catch (error) {
    uni.showToast({ title: "网络错误", icon: "none" });
  } finally {
    uni.hideLoading();
  }
}

// 底部操作按钮逻辑
const applyButton = computed(() => {
  if (!gig.value) {
    return { text: "加载中...", disabled: true };
  }
  if (hasApplied.value) {
    return { text: "已报名", disabled: true };
  }

  switch (gig.value.status) {
    case GigStatus.Recruiting:
      return { text: "立即报名", disabled: false };
    case GigStatus.Paused:
      return { text: "暂停招募", disabled: true };
    case GigStatus.Locked:
      return { text: "招募截止", disabled: true };
    case GigStatus.InProgress:
      return { text: "进行中", disabled: true };
    case GigStatus.Completed:
      return { text: "已完成", disabled: true };
    case GigStatus.Closed:
      return { text: "已关闭", disabled: true };
    default:
      return { text: "无法报名", disabled: true };
  }
});

async function handleApply() {
  if (!gig.value || applyButton.value.disabled) return;

  const { user } = userStore;
  if (!user?.nickname || !user?.mobile) {
    uni.showModal({
      title: "提示",
      content: "请先在“我的”页面完善您的姓名和联系方式再进行报名。",
      showCancel: false,
      confirmText: "知道了",
    });
    return;
  }

  uni.showModal({
    title: "确认报名",
    content: `您确定要报名【${gig.value.title}】吗？`,
    success: async (res) => {
      if (res.confirm) {
        uni.showLoading({ title: "正在提交..." });
        try {
          await applyForGig({
            gig_id: gig.value.id,
            message: "我对此很感兴趣，希望获得这个机会！",
            applicant_name: user.nickname,
            applicant_phone: user.mobile,
          });
          uni.hideLoading();
          uni.showToast({ title: "报名成功！", icon: "success" });
          hasApplied.value = true; // 更新申请状态
          fetchGigDetail(); // 重新获取数据以更新状态
        } catch (error: any) {
          uni.hideLoading();
          console.error("handleApply error:", error);
          uni.showToast({
            title: error.message || "报名失败，请重试",
            icon: "none",
          });
        }
      }
    },
  });
}

// 辅助函数和计算属性
function formatTime(start: string, end: string): string {
  const startTime = dayjs(start);
  const endTime = dayjs(end);
  if (startTime.isSame(endTime, "day")) {
    return `${startTime.format("YYYY-MM-DD HH:mm")} - ${endTime.format(
      "HH:mm"
    )}`;
  }
  return `${startTime.format("YYYY-MM-DD HH:mm")} - ${endTime.format(
    "YYYY-MM-DD HH:mm"
  )}`;
}
</script>

<style lang="scss" scoped>
.gig-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f8fa;
}
.detail-scroll-view {
  flex: 1;
  padding: 24rpx;
  box-sizing: border-box;
}
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.main-info-card {
  .gig-title {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
  }
  .gig-price {
    display: flex;
    align-items: baseline;
    color: #fa3534;
    margin-bottom: 16rpx;
    .price-value {
      font-size: 48rpx;
      font-weight: bold;
    }
    .price-unit {
      font-size: 28rpx;
      margin-left: 8rpx;
    }
  }
  .gig-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    .tag {
      background-color: #f3f4f6;
      color: var(--text-secondary);
      padding: 6rpx 16rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
    }
    .urgent-tag {
      background-color: #fa35341a;
      color: #fa3534;
      font-weight: bold;
    }
  }
}
.detail-info-card .detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  &:last-child {
    margin-bottom: 0;
  }
  .detail-icon {
    font-size: 36rpx;
    color: var(--text-info);
    margin-right: 24rpx;
    margin-top: 4rpx;
  }
  view {
    display: flex;
    flex-direction: column;
    .item-label {
      font-size: 28rpx;
      color: var(--text-info);
      margin-bottom: 4rpx;
    }
    .item-value {
      font-size: 30rpx;
    }
  }
}
.card-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  display: block;
}
.description-card .description-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-secondary);
}
.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 24rpx;
  .gallery-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 8rpx;
  }
}
.requirement-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  .req-item {
    font-size: 28rpx;
    .req-label {
      color: var(--text-info);
    }
  }
}
.contact-card .contact-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  margin-bottom: 16rpx;
  .detail-icon {
    margin-right: 16rpx;
  }
}
.employer-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .employer-content {
    display: flex;
    align-items: center;
  }
  .employer-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 24rpx;
  }
  .employer-details {
    display: flex;
    flex-direction: column;
    .employer-name {
      font-size: 30rpx;
      font-weight: bold;
    }
  }
  .arrow-icon {
    font-size: 36rpx;
    color: #ccc;
  }
}
.bottom-action-bar {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 48rpx;
    font-size: 22rpx;
    color: var(--text-secondary);
    .action-icon {
      font-size: 40rpx;
      margin-bottom: 4rpx;
    }
  }
  .apply-button {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #07c160;
    color: #fff;
    border-radius: 40rpx;
    text-align: center;
    font-size: 30rpx;
    font-weight: bold;
    &.disabled-button {
      background-color: #ccc;
      color: #999;
    }
  }
}
.status-badge {
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
}
.status-recruiting {
  background-color: var(--green);
}
.status-paused {
  background-color: var(--yellow);
}
.status-locked {
  background-color: var(--blue);
}
.status-ongoing {
  background-color: var(--blue);
}
.status-completed {
  background-color: var(--text-tertiary);
}
.status-closed {
  background-color: var(--red);
}
.status-draft {
  background-color: var(--text-tertiary);
}
</style>

<template>
  <view class="gig-card bg-card shadow-sm">
    <view class="card-content">
      <!-- 卡片头部：标题和薪酬 -->
      <view class="header">
        <text class="title text-base font-bold">{{ gig.title }}</text>
        <view class="price">
          <text class="price-value text-primary font-bold">
            {{ formattedSalary }}
          </text>
        </view>
      </view>

      <!-- 工作描述 -->
      <view v-if="gig.description" class="description text-secondary">
        {{ truncatedDescription }}
      </view>

      <!-- 标签 -->
      <view v-if="gig.tags && gig.tags.length > 0" class="tags">
        <view v-for="(tag, index) in gig.tags" :key="index" class="tag tag-sm">
          {{ tag }}
        </view>
        <view v-if="gig.is_urgent" class="tag tag-urgent">紧急</view>
      </view>

      <!-- 工作要求标签 -->
      <view class="requirement-tags">
        <view v-if="gig.experience" class="tag tag-sm tag-outlined">
          {{ getExperienceText(gig.experience) }}
        </view>
        <view v-if="gig.gender" class="tag tag-sm tag-outlined">
          {{ getGenderText(gig.gender) }}
        </view>
        <view v-if="gig.age_min || gig.age_max" class="tag tag-sm tag-outlined">
          {{ ageRange }}
        </view>
      </view>

      <!-- 工作时间 -->
      <view v-if="gig.work_duration" class="work-time">
        <text class="i-carbon-time text-info mr-1"></text>
        <text class="text-secondary text-sm">{{ formattedWorkDuration }}</text>
      </view>

      <!-- 卡片底部：位置和发布者信息 -->
      <view class="footer">
        <view class="location">
          <text class="i-carbon-location-current text-info"></text>
          <text class="text-info text-sm">
            {{ gig.address_name || gig.full_address }}
          </text>
        </view>

        <!-- 发布者信息 -->
        <view v-if="gig.publisher" class="employer">
          <image :src="gig.publisher.avatar" class="avatar" />
          <text class="name text-secondary">
            {{ gig.publisher.nickname || "匿名用户" }}
          </text>
        </view>
      </view>

      <!-- 申请状态（仅在需要时显示） -->
      <view v-if="showStatus && applicationStatus" class="status-bar">
        <text :class="getStatusClass(applicationStatus)">
          {{ getStatusText(applicationStatus) }}
        </text>
      </view>

      <!-- 统计信息（仅在需要时显示） -->
      <view v-if="showStats" class="stats-bar">
        <text class="stats-text">
          已报名 {{ gig.current_people_count }}/{{ gig.people_count }}
        </text>
        <text class="stats-text">
          {{ formatRelativeTime(gig.created_at) }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { Gig } from "@/types/gig";
import {
  formatSalary,
  formatWorkDuration,
  getStatusText,
  getStatusClass,
} from "@/utils/gig";
import { GIG_GENDER_MAP, GIG_EXPERIENCE_MAP } from "@/constants/gig";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import "dayjs/locale/zh-cn";

dayjs.extend(relativeTime);
dayjs.locale("zh-cn");

interface Props {
  gig: Gig;
  mode?: "seeker" | "recruiter";
  showStatus?: boolean;
  showStats?: boolean;
  applicationStatus?: string;
}

const props = withDefaults(defineProps<Props>(), {
  mode: "seeker",
  showStatus: false,
  showStats: false,
});

// 格式化薪酬显示
const formattedSalary = computed(() => {
  return formatSalary(props.gig.salary, props.gig.salary_unit.toString());
});

// 格式化工作时长显示
const formattedWorkDuration = computed(() => {
  return formatWorkDuration(props.gig.start_time, props.gig.end_time);
});

// 截断的描述
const truncatedDescription = computed(() => {
  if (!props.gig.description) return "";
  return props.gig.description.length > 50
    ? props.gig.description.substring(0, 50) + "..."
    : props.gig.description;
});

// 年龄范围显示
const ageRange = computed(() => {
  const { age_min, age_max } = props.gig;
  if (age_min && age_max) {
    return `${age_min}-${age_max}岁`;
  } else if (age_min) {
    return `${age_min}岁以上`;
  } else if (age_max) {
    return `${age_max}岁以下`;
  }
  return "";
});

// 获取性别文本
const getGenderText = (gender: number): string => {
  return GIG_GENDER_MAP[gender] || "不限";
};

// 获取经验文本
const getExperienceText = (experience: number): string => {
  return GIG_EXPERIENCE_MAP[experience] || "不限";
};

// 格式化相对时间
const formatRelativeTime = (time: string): string => {
  return dayjs(time).fromNow();
};
</script>

<style lang="scss" scoped>
.gig-card {
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-12);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 var(--spacing-8) var(--spacing-24) rgba(0, 0, 0, 0.12);
  }
}

.card-content {
  padding: var(--spacing-16);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-12);
}

.title {
  flex: 1;
  margin-right: var(--spacing-12);
  line-height: 1.4;
}

.price {
  flex-shrink: 0;
}

.price-value {
  font-size: var(--font-size-lg);
}

.description {
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin-bottom: var(--spacing-12);
}

.tags,
.requirement-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-12);
}

.tag {
  font-size: var(--font-size-xs);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-base);
  background-color: var(--bg-info-light);
  color: var(--text-blue);

  &.tag-outlined {
    background-color: transparent;
    border: 1rpx solid var(--border-color);
    color: var(--text-secondary);
  }

  &.tag-urgent {
    background-color: var(--bg-warning-light);
    color: var(--text-warning);
  }
}

.work-time {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-12);
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.employer {
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
}

.avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 1rpx solid var(--border-color);
}

.name {
  font-size: var(--font-size-sm);
}

.status-bar {
  margin-top: var(--spacing-12);
  padding-top: var(--spacing-12);
  border-top: 1rpx solid var(--border-color);
}

.stats-bar {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-12);
  padding-top: var(--spacing-12);
  border-top: 1rpx solid var(--border-color);
}

.stats-text {
  font-size: var(--font-size-xs);
  color: var(--text-info);
}
</style>

<template>
  <view class="gig-manage-page bg-page min-h-screen">
    <!-- 统计概览 -->
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-icon recruiting">
            <text class="i-carbon-document-tasks"></text>
          </view>
          <text class="stat-number">{{ totalPublished }}</text>
          <text class="stat-label">已发布</text>
        </view>
        <view class="stat-item">
          <view class="stat-icon active">
            <text class="i-carbon-play-filled"></text>
          </view>
          <text class="stat-number">{{ recruitingCount }}</text>
          <text class="stat-label">招聘中</text>
        </view>
        <view class="stat-item">
          <view class="stat-icon pending">
            <text class="i-carbon-user-follow"></text>
          </view>
          <text class="stat-number">{{ totalApplicants }}</text>
          <text class="stat-label">总报名</text>
        </view>
      </view>
    </view>

    <!-- 筛选器 -->
    <view class="filter-section">
      <view class="filter-wrapper">
        <view
          v-for="filter in statusFilters"
          :key="filter.value"
          class="filter-item"
          :class="{ active: currentFilter === filter.value }"
          @tap="setFilter(filter.value)"
        >
          <view class="filter-content">
            <text class="filter-text">{{ filter.name }}</text>
            <text v-if="filter.count > 0" class="filter-count">{{
              filter.count
            }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 发布列表 -->
    <view class="content-section">
      <view v-if="filteredGigs.length > 0" class="published-list">
        <view
          v-for="gig in filteredGigs"
          :key="gig.id"
          class="gig-item card"
          @tap="viewDetail(gig.id)"
        >
          <!-- 卡片头部 -->
          <view class="item-header">
            <view class="title-area">
              <text class="item-title">{{ gig.title }}</text>
            </view>
            <view class="header-right">
              <view class="status-badge" :class="gig.status">
                <text>{{ getStatusText(gig.status) }}</text>
              </view>
              <view class="price-area">
                <text class="price-amount">¥{{ gig.price }}</text>
                <text class="price-unit">/{{ gig.priceUnit }}</text>
              </view>
            </view>
          </view>

          <view class="item-body">
            <!-- 工作时间信息 -->
            <view class="work-time-section">
              <view class="time-item">
                <text class="i-carbon-time info-icon"></text>
                <text class="time-label">{{ getWorkTimeDisplay(gig) }}</text>
              </view>
            </view>

            <!-- 基本要求标签 -->
            <view class="requirements-section">
              <view class="requirements-tags">
                <view class="requirement-tag">{{ gig.requirements.age }}</view>
                <view class="requirement-tag">{{ gig.requirements.education }}</view>
                <view class="requirement-tag">{{ gig.requirements.experience }}</view>
                <view class="requirement-tag">{{ gig.requirements.gender }}</view>
              </view>
            </view>

            <!-- 核心信息 -->
            <view class="item-info">
              <view class="info-row">
                <view class="info-item">
                  <text class="i-carbon-location info-icon"></text>
                  <text class="info-text">{{ gig.location }}</text>
                </view>
                <view class="info-item">
                  <text class="i-carbon-user-multiple info-icon"></text>
                  <text class="info-text">{{ gig.applicantsCount }}人报名</text>
                </view>
              </view>
              <view class="info-row">
                <view class="info-item">
                  <text class="i-carbon-view info-icon"></text>
                  <text class="info-text">{{ gig.viewCount }}次浏览</text>
                </view>
                <view class="info-item">
                  <text class="i-carbon-time info-icon"></text>
                  <text class="info-text">发布于{{ formatTime(gig.createdTime) }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="item-actions">
            <template v-if="gig.status === 'recruiting'">
              <view
                class="action-btn primary"
                @tap.stop="navigateToApplicants(gig.id)"
              >
                <text class="i-carbon-user-multiple"></text>
                <text>查看报名({{ gig.applicantsCount }})</text>
              </view>
              <view class="action-btn secondary" @tap.stop="editGig(gig.id)">
                <text class="i-carbon-edit"></text>
                <text>编辑</text>
              </view>
              <view class="action-btn danger" @tap.stop="closeGig(gig.id)">
                <text class="i-carbon-stop"></text>
                <text>停止招聘</text>
              </view>
            </template>

            <template v-else-if="gig.status === 'working'">
              <view
                class="action-btn success"
                @tap.stop="navigateToApplicants(gig.id)"
              >
                <text class="i-carbon-user-follow"></text>
                <text>联系工人</text>
              </view>
              <view class="action-btn warning" @tap.stop="completeGig(gig.id)">
                <text class="i-carbon-checkmark-filled"></text>
                <text>标记完成</text>
              </view>
            </template>

            <template v-else-if="gig.status === 'expired'">
              <view
                class="action-btn secondary"
                @tap.stop="republishGig(gig.id)"
              >
                <text class="i-carbon-restart"></text>
                <text>重新发布</text>
              </view>
              <view class="action-btn danger" @tap.stop="deleteGig(gig.id)">
                <text class="i-carbon-trash-can"></text>
                <text>删除</text>
              </view>
            </template>

            <template v-else-if="gig.status === 'completed'">
              <view class="action-btn info" @tap.stop="viewFeedback(gig.id)">
                <text class="i-carbon-star"></text>
                <text>查看评价</text>
              </view>
              <view
                class="action-btn secondary"
                @tap.stop="republishGig(gig.id)"
              >
                <text class="i-carbon-restart"></text>
                <text>再次发布</text>
              </view>
            </template>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <view class="empty-icon">
          <text v-if="currentFilter === 'all'" class="i-carbon-add-alt"></text>
          <text v-else class="i-carbon-search"></text>
        </view>
        <text class="empty-title">
          {{
            currentFilter === "all"
              ? "暂无发布的零工"
              : `暂无${getCurrentFilterName()}的零工`
          }}
        </text>
        <text class="empty-desc">
          {{
            currentFilter === "all"
              ? "发布零工信息，快速找到合适的工人"
              : "可以尝试切换其他筛选条件"
          }}
        </text>
        <view
          v-if="currentFilter === 'all'"
          class="empty-action"
          @tap="goToPublish"
        >
          <text>立即发布</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import CustomNavBar from "@/components/CustomNavBar.vue";
import dayjs from "dayjs";

// 筛选状态
const currentFilter = ref("all");

// 统计数据
const totalPublished = ref(0);
const recruitingCount = ref(0);
const totalApplicants = ref(0);

// 发布列表数据
const publishedGigs = ref([
  {
    id: 1,
    title: "周末招聘传单派发员，薪资日结",
    price: "150",
    priceUnit: "天",
    location: "朝阳区·三里屯",
    workTime: "9:00-17:00",
    workDate: "2024-01-20 至 2024-01-21",
    status: "recruiting",
    applicantsCount: 5,
    viewCount: 28,
    createdTime: dayjs().subtract(2, "day").toISOString(),
    tags: ["日结", "学生可", "男女不限"],
    requirements: {
      age: "18-35岁",
      education: "不限",
      experience: "无需经验",
      gender: "男女不限"
    },
  },
  {
    id: 2,
    title: "急招临时搬家师傅，自带车辆优先",
    price: "300",
    priceUnit: "次",
    location: "海淀区·中关村",
    workTime: "8:00-18:00",
    workDate: "2024-01-19",
    status: "working",
    applicantsCount: 1,
    viewCount: 15,
    createdTime: dayjs().subtract(1, "day").toISOString(),
    tags: ["急招", "自带车", "体力活"],
    requirements: {
      age: "25-45岁",
      education: "不限",
      experience: "1年以上",
      gender: "男性优先"
    },
  },
  {
    id: 3,
    title: "家庭日常保洁，两室一厅",
    price: "40",
    priceUnit: "小时",
    location: "西城区·金融街",
    workTime: "14:00-17:00",
    workDate: "长期有效",
    status: "expired",
    applicantsCount: 0,
    viewCount: 8,
    createdTime: dayjs().subtract(7, "day").toISOString(),
    tags: ["长期稳定", "女性优先"],
    requirements: {
      age: "30-50岁",
      education: "不限",
      experience: "有经验优先",
      gender: "女性优先"
    },
  },
  {
    id: 4,
    title: "餐厅服务员，包吃包住",
    price: "4500",
    priceUnit: "月",
    location: "丰台区·方庄",
    workTime: "全职",
    workDate: "长期招聘",
    status: "completed",
    applicantsCount: 3,
    viewCount: 45,
    createdTime: dayjs().subtract(15, "day").toISOString(),
    tags: ["包吃住", "长期", "服务业"],
    requirements: {
      age: "20-35岁",
      education: "初中以上",
      experience: "无需经验",
      gender: "男女不限"
    },
  },
]);

// 筛选选项
const statusFilters = computed(() => [
  { name: "全部", value: "all", count: publishedGigs.value.length },
  {
    name: "招聘中",
    value: "recruiting",
    count: publishedGigs.value.filter((g) => g.status === "recruiting").length,
  },
  {
    name: "进行中",
    value: "working",
    count: publishedGigs.value.filter((g) => g.status === "working").length,
  },
  {
    name: "已完成",
    value: "completed",
    count: publishedGigs.value.filter((g) => g.status === "completed").length,
  },
  {
    name: "已过期",
    value: "expired",
    count: publishedGigs.value.filter((g) => g.status === "expired").length,
  },
]);

// 筛选后的列表
const filteredGigs = computed(() => {
  if (currentFilter.value === "all") {
    return publishedGigs.value;
  }
  return publishedGigs.value.filter(
    (gig) => gig.status === currentFilter.value
  );
});

// 方法
const setFilter = (filterValue: string) => {
  currentFilter.value = filterValue;
};

const getCurrentFilterName = () => {
  const filter = statusFilters.value.find(
    (f) => f.value === currentFilter.value
  );
  return filter ? filter.name : "";
};

const getStatusText = (status: string) => {
  const statusMap = {
    recruiting: "招聘中",
    working: "进行中",
    completed: "已完成",
    expired: "已过期",
  };
  return statusMap[status] || status;
};

const formatTime = (time: string) => {
  const now = dayjs();
  const targetTime = dayjs(time);
  const diffDays = now.diff(targetTime, "day");

  if (diffDays === 0) {
    return "今天";
  } else if (diffDays === 1) {
    return "昨天";
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return targetTime.format("MM-DD");
  }
};

const getWorkTimeDisplay = (gig: any) => {
  const workTime = gig.workTime;
  const workDate = gig.workDate;
  
  // 判断是否跨天
  const isMultiDay = workDate.includes('至') || workDate.includes('-') || workDate.includes('长期');
  
  if (isMultiDay) {
    return `${workTime} | ${workDate}`;
  } else {
    return `${workTime} | ${workDate}`;
  }
};

const viewDetail = (gigId: number) => {
  uni.navigateTo({ url: `/pages/gig/detail?id=${gigId}` });
};

const navigateToApplicants = (gigId: number) => {
  uni.navigateTo({ url: `/pages/gig/applicants?gigId=${gigId}` });
};

const editGig = (gigId: number) => {
  uni.navigateTo({ url: `/pages/gig/edit?id=${gigId}` });
};

const closeGig = (gigId: number) => {
  uni.showModal({
    title: "确认操作",
    content: "确定要停止这个零工的招聘吗？",
    success: (res) => {
      if (res.confirm) {
        // TODO: 调用API停止招聘
        const gigIndex = publishedGigs.value.findIndex((g) => g.id === gigId);
        if (gigIndex !== -1) {
          publishedGigs.value[gigIndex].status = "expired";
        }
        uni.showToast({ title: "已停止招聘", icon: "success" });
      }
    },
  });
};

const completeGig = (gigId: number) => {
  uni.showModal({
    title: "确认完成",
    content: "确定标记这个零工为已完成吗？",
    success: (res) => {
      if (res.confirm) {
        // TODO: 调用API标记完成
        const gigIndex = publishedGigs.value.findIndex((g) => g.id === gigId);
        if (gigIndex !== -1) {
          publishedGigs.value[gigIndex].status = "completed";
        }
        uni.showToast({ title: "已标记完成", icon: "success" });
      }
    },
  });
};

const republishGig = (gigId: number) => {
  uni.navigateTo({ url: `/pages/gig/republish?id=${gigId}` });
};

const deleteGig = (gigId: number) => {
  uni.showModal({
    title: "确认删除",
    content: "删除后无法恢复，确定要删除这个零工吗？",
    success: (res) => {
      if (res.confirm) {
        // TODO: 调用API删除
        const gigIndex = publishedGigs.value.findIndex((g) => g.id === gigId);
        if (gigIndex !== -1) {
          publishedGigs.value.splice(gigIndex, 1);
        }
        uni.showToast({ title: "删除成功", icon: "success" });
      }
    },
  });
};

const viewFeedback = (gigId: number) => {
  uni.navigateTo({ url: `/pages/gig/feedback?id=${gigId}` });
};

const goToPublish = () => {
  uni.navigateTo({ url: "/pages/gig/publish" });
};

// 计算统计数据
const updateStats = () => {
  totalPublished.value = publishedGigs.value.length;
  recruitingCount.value = publishedGigs.value.filter(
    (g) => g.status === "recruiting"
  ).length;
  totalApplicants.value = publishedGigs.value.reduce(
    (sum, gig) => sum + gig.applicantsCount,
    0
  );
};

onMounted(() => {
  updateStats();
});
</script>

<style lang="scss" scoped>
.gig-manage-page {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.stats-section {
  padding: var(--spacing-16);
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-12);
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-16);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.stat-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-8);
  font-size: var(--font-size-lg);
  color: white;

  &.recruiting {
    background-color: var(--text-blue);
  }

  &.active {
    background-color: var(--text-green);
  }

  &.pending {
    background-color: var(--text-yellow);
  }
}

.stat-number {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-bottom: var(--spacing-4);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.filter-section {
  padding: 0 var(--spacing-16) var(--spacing-16);
}

.filter-wrapper {
  display: flex;
  gap: var(--spacing-8);
  padding: var(--spacing-8);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow-x: auto;
  white-space: nowrap;
}

.filter-item {
  flex: none;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8) var(--spacing-16);
  border-radius: var(--radius-base);
  transition: all 0.3s ease;
  white-space: nowrap;

  &.active {
    background-color: var(--primary);
    color: white;

    .filter-text {
      color: white;
    }

    .filter-count {
      color: white;
      opacity: 0.8;
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

.filter-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.filter-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-base);
  white-space: nowrap;
}

.filter-count {
  font-size: var(--font-size-xs);
  color: var(--text-info);
  font-weight: var(--font-weight-bold);
}

.content-section {
  padding: 0 var(--spacing-16) var(--spacing-16);
}

.published-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-16);
}

.gig-item {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-16);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-12);
  margin-bottom: var(--spacing-12);
}

.title-area {
  flex: 1;
}

.item-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  line-height: var(--line-height-tight);
}

.header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-4);
}

.status-badge {
  display: inline-block;
  padding: var(--spacing-6) var(--spacing-12);
  border-radius: 16rpx;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);

  &.recruiting {
    background-color: var(--bg-success-light);
    color: var(--text-green);
  }

  &.working {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
  }

  &.completed {
    background-color: var(--bg-grey-light);
    color: var(--text-grey);
  }

  &.expired {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }
}

.price-area {
  text-align: right;
}

.price-amount {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--primary);
}

.price-unit {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.item-body {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-12);
  margin-bottom: var(--spacing-12);
}

.work-time-section {
  padding: var(--spacing-8) var(--spacing-12);
  background-color: var(--bg-grey-light);
  border-radius: var(--radius-base);
}

.time-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.time-label {
  font-size: var(--font-size-sm);
  color: var(--text-base);
  font-weight: var(--font-weight-medium);
}

.requirements-section {
  /* 样式已在 .item-body 中处理 */
}

.requirements-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-12);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-base);
}

.requirements-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-8);
}

.requirement-tag {
  padding: var(--spacing-6) var(--spacing-12);
  background-color: var(--bg-primary-light);
  color: var(--primary);
  border-radius: 16rpx;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.item-info {
  /* 样式已在 .item-body 中处理 */
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-16);

  &:not(:last-child) {
    margin-bottom: var(--spacing-4);
  }
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  flex: 1;
  min-width: 0;
}

.info-icon {
  font-size: var(--font-size-base);
  color: var(--text-info);
}

.info-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.item-actions {
  display: flex;
  gap: var(--spacing-8);
  flex-wrap: wrap;
  justify-content: flex-end;
  border-top: 1rpx solid var(--border-color);
  padding-top: var(--spacing-16);
  margin-top: var(--spacing-4);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-8) var(--spacing-12);
  border-radius: 16rpx;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;

  &.primary {
    background-color: var(--primary);
    color: white;
  }

  &.secondary {
    background-color: var(--bg-grey-light);
    color: var(--text-grey);
  }

  &.success {
    background-color: var(--bg-success-light);
    color: var(--text-green);
  }

  &.warning {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
  }

  &.danger {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
  }

  &.info {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
  }

  &:active {
    transform: scale(0.95);
  }
}

.empty-state {
  text-align: center;
  padding: var(--spacing-60) var(--spacing-20);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
}

.empty-icon {
  font-size: 120rpx;
  color: var(--text-grey);
  margin-bottom: var(--spacing-16);
}

.empty-title {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-bottom: var(--spacing-8);
}

.empty-desc {
  display: block;
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-24);
  line-height: var(--line-height-normal);
}

.empty-action {
  display: inline-block;
  padding: var(--spacing-12) var(--spacing-24);
  background-color: var(--primary);
  color: white;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background-color: var(--primary-700);
  }
}
</style>

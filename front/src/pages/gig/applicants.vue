<template>
  <view class="applicants-page bg-page min-h-screen">
    <CustomNavBar title="报名管理" />

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <u-loading-icon mode="circle" size="24" />
      <text class="ml-2 text-gray-500">正在加载...</text>
    </view>

    <!-- 列表内容 -->
    <view v-else class="p-4">
      <view v-if="applicants.length > 0" class="applicants-list space-y-3">
        <view
          v-for="applicant in applicants"
          :key="applicant.id"
          class="applicant-item card bg-white rounded-xl shadow-md p-4"
        >
          <!-- 申请人信息 -->
          <view
            class="flex items-center justify-between mb-3"
            @tap="goToApplicantProfile(applicant.user_id)"
          >
            <view class="flex items-center">
              <image
                :src="applicant.applicant_info?.avatar || defaultAvatar"
                class="avatar w-12 h-12 rounded-full mr-3"
              />
              <view>
                <text class="name font-semibold text-lg">{{
                  applicant.applicant_info?.nickname || "匿名用户"
                }}</text>
                <!-- 可以后续加入评分等信息 -->
              </view>
            </view>
            <view
              :style="{ color: getStatusDetails(applicant.status).color }"
              class="status-badge text-sm font-bold"
            >
              <text>{{ getStatusDetails(applicant.status).text }}</text>
            </view>
          </view>

          <!-- 申请详情 -->
          <view class="details mb-4 text-gray-600 text-sm space-y-1">
            <view class="detail-row flex items-center">
              <view class="i-carbon-phone detail-icon mr-2" />
              <text class="detail-text">{{ applicant.applicant_phone }}</text>
            </view>
            <view class="detail-row flex items-center">
              <view class="i-carbon-time detail-icon mr-2" />
              <text class="detail-text">{{
                formatApplyTime(applicant.created_at)
              }}</text>
            </view>
            <view
              v-if="applicant.message"
              class="message-row mt-2 p-2 bg-gray-100 rounded-md"
            >
              <text class="message-text">{{ applicant.message }}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="actions flex items-center justify-end space-x-3">
            <template
              v-if="applicant.status === GIG_APPLICATION_STATUS.PENDING"
            >
              <button
                class="action-btn text-sm bg-red-100 text-red-600 px-4 py-1 rounded-full"
                @click.stop="
                  handleUpdateStatus(
                    applicant.id,
                    GIG_APPLICATION_STATUS.REJECTED
                  )
                "
              >
                拒绝
              </button>
              <button
                class="action-btn text-sm bg-green-100 text-green-600 px-4 py-1 rounded-full"
                @click.stop="
                  handleUpdateStatus(
                    applicant.id,
                    GIG_APPLICATION_STATUS.CONFIRMED
                  )
                "
              >
                录用
              </button>
            </template>
            <template v-else>
              <button
                class="action-btn text-sm bg-blue-100 text-blue-600 px-4 py-1 rounded-full"
                @click.stop="contactApplicant(applicant)"
              >
                联系Ta
              </button>
            </template>
          </view>
        </view>
      </view>
      <view v-else class="empty-state text-center py-20">
        <u-empty mode="data" text="暂无报名信息" />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { listGigApplications, reviewApplication } from "@/api/gig";
import type { GigApplication } from "@/types/gig";
import {
  GigApplicationStatus,
  getGigApplicationStatusDetails,
} from "@/constants/gig";
import dayjs from "dayjs";
import defaultAvatar from "@/static/images/default-avatar.png";

const gigId = ref<number | null>(null);
const applicants = ref<GigApplication[]>([]);
const loading = ref(true);

onLoad(async (options) => {
  if (options && options.gigId) {
    gigId.value = parseInt(options.gigId, 10);
    await fetchApplicants();
  } else {
    loading.value = false;
    uni.showToast({ title: "无效的零工ID", icon: "none" });
  }
});

const fetchApplicants = async () => {
  if (!gigId.value) return;
  loading.value = true;
  try {
    const res = await listGigApplications({
      gig_id: gigId.value,
      page: 1,
      page_size: 100,
    }); // 获取全部申请者
    if (res.code === 0) {
      applicants.value = res.data.list;
    }
  } catch (error) {
    console.error("获取申请列表失败", error);
  } finally {
    loading.value = false;
  }
};

const getStatusDetails = (status: GigApplicationStatus) => {
  return getGigApplicationStatusDetails(status, "recruiter");
};

const handleUpdateStatus = async (
  applicationId: number,
  newStatus: GigApplicationStatus
) => {
  uni.showLoading({ title: "处理中..." });
  try {
    const { code, message, data } = await reviewApplication({
      application_id: applicationId,
      new_status: newStatus,
    });
    if (code === 0) {
      // 找到对应的申请并更新其状态，避免重新请求整个列表
      const index = applicants.value.findIndex((a) => a.id === applicationId);
      if (index !== -1) {
        applicants.value[index].status = newStatus;
      } else {
        // 如果找不到（理论上不应该），则刷新整个列表
        await fetchApplicants();
      }
    } else {
      uni.showToast({ title: message || "操作失败", icon: "none" });
    }
  } catch (error) {
    console.error("更新申请状态失败", error);
    uni.showToast({ title: "操作失败，请重试", icon: "none" });
  } finally {
    uni.hideLoading();
  }
};

const goToApplicantProfile = (userId?: number) => {
  if (!userId) return;
  // uni.navigateTo({ url: `/pages/user/profile?id=${userId}` });
  console.log(`跳转到用户详情页: ${userId}`);
};

const formatApplyTime = (applyTime: string) => {
  return dayjs(applyTime).format("YYYY-MM-DD HH:mm");
};

const contactApplicant = (applicant: GigApplication) => {
  uni.makePhoneCall({
    phoneNumber: applicant.applicant_phone,
    success: () => {
      console.log("拨打电话成功");
    },
    fail: (err) => {
      console.log("拨打电话失败", err);
      uni.showToast({ title: "拨号功能不可用", icon: "none" });
    },
  });
};
</script>

<style lang="scss" scoped>
// 使用 uno.css 原子化类，减少样式代码
</style>

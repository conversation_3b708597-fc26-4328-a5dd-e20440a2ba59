<template>
  <view class="container">
    <view class="content">
      <tui-form ref="formRef" :model="formData" :rules="formRules">
        <!-- 基本信息 -->
        <view class="card">
          <view class="section-title">
            <text>基本信息</text>
          </view>

          <form-input
            prop="title"
            label="工作标题"
            :asterisk="true"
            v-model="formData.title"
            placeholder="请输入工作标题"
          />

          <form-input
            prop="start_time"
            label="开始时间"
            :asterisk="true"
            :arrow="true"
            @click="showStartTimePicker"
          >
            <view
              class="picker-input"
              :class="{ placeholder: !formData.start_time }"
            >
              {{ formData.start_time || "请选择开始时间" }}
            </view>
          </form-input>

          <form-input
            prop="end_time"
            label="结束时间"
            :asterisk="true"
            :arrow="true"
            @click="showEndTimePicker"
          >
            <view
              class="picker-input"
              :class="{ placeholder: !formData.end_time }"
            >
              {{ formData.end_time || "请选择结束时间" }}
            </view>
          </form-input>
          <form-input
            prop="address"
            label="工作地点"
            :asterisk="true"
            v-model="formData.address"
            placeholder="点击选择地点"
            :arrow="true"
            @tap="selectLocation"
            :disabled="true"
          />
          <form-input
            prop="detail_address"
            label="详细地址"
            :asterisk="true"
            v-model="formData.detail_address"
            placeholder="请输入楼号、楼层、门牌号等"
          />
          <form-input
            prop="people_count"
            label="招聘人数"
            :asterisk="true"
            v-model.number="formData.people_count"
            placeholder="请输入招聘人数"
            type="number"
            unit="人"
          />
          <!-- 合并薪酬和单位 -->
          <form-input prop="salary" label="工作薪酬" :asterisk="true">
            <view class="salary-input-group">
              <input
                v-model.number="formData.salary"
                class="salary-input"
                type="number"
                placeholder="请输入金额"
              />
              <picker
                class="salary-picker"
                :value="salaryUnitPickerValue"
                :range="salaryUnitPickerOptions"
                @change="onSalaryUnitChange"
              >
                <view class="picker-input">
                  {{ selectedSalaryUnitLabel }}
                </view>
              </picker>
            </view>
          </form-input>

          <form-input
            prop="settlement"
            label="结算方式"
            :asterisk="true"
            :arrow="true"
            :bottomBorder="false"
          >
            <picker
              :value="settlementPickerValue"
              :range="settlementPickerOptions"
              @change="onSettlementChange"
            >
              <view
                class="picker-input"
                :class="{ placeholder: !selectedSettlementLabel }"
                >{{ selectedSettlementLabel || "请选择结算方式" }}</view
              >
            </picker>
          </form-input>
        </view>

        <!-- 招聘要求 -->
        <view class="card">
          <view class="section-title">招聘要求</view>
          <form-input prop="gender" label="性别要求" :arrow="true">
            <picker
              :value="genderPickerValue"
              :range="genderPickerOptions"
              @change="onGenderChange"
            >
              <view
                class="picker-input"
                :class="{
                  placeholder: !formData.gender,
                }"
                >{{ selectedGenderLabel }}</view
              >
            </picker>
          </form-input>

          <!-- 年龄要求待实现 -->

          <form-input prop="education" label="学历要求" :arrow="true">
            <picker
              :value="educationPickerValue"
              :range="educationPickerOptions"
              @change="onEducationChange"
            >
              <view
                class="picker-input"
                :class="{ placeholder: !formData.education }"
                >{{ selectedEducationLabel }}</view
              >
            </picker>
          </form-input>
          <form-input prop="experience" label="工作经验" :arrow="true">
            <picker
              :value="experiencePickerValue"
              :range="experiencePickerOptions"
              @change="onExperienceChange"
            >
              <view
                class="picker-input"
                :class="{ placeholder: !formData.experience }"
              >
                {{ selectedExperienceLabel }}
              </view>
            </picker>
          </form-input>

          <!-- 健康证待实现 -->
        </view>

        <!-- 工作描述 -->
        <view class="card description-card">
          <view class="description-section">
            <view class="description-label">
              <text>工作描述</text>
              <text class="char-count"
                >{{ formData.description.length }}/500</text
              >
            </view>
            <textarea
              v-model="formData.description"
              placeholder="请详细描述工作内容、要求等信息"
              class="description-textarea"
              maxlength="500"
              placeholder-class="textarea-placeholder"
            />
          </view>
        </view>

        <!-- 联系方式 -->
        <view class="card">
          <view class="section-title">
            <text>联系方式</text>
          </view>

          <form-input
            prop="contact_name"
            label="联系人"
            :asterisk="true"
            v-model="formData.contact_name"
            placeholder="请输入联系人姓名"
          />

          <form-input
            prop="contact_phone"
            label="联系电话"
            :asterisk="true"
            v-model="formData.contact_phone"
            placeholder="请输入联系电话"
          />
        </view>
      </tui-form>

      <!-- 协议确认 -->
      <view class="card">
        <view class="agreement-item" @click="toggleAgreement">
          <view class="checkbox" :class="{ checked: agreedToTerms }">
            <text v-if="agreedToTerms">✓</text>
          </view>
          <view class="agreement-text">
            我已阅读并同意《用户服务协议》和《隐私政策》
          </view>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="submit-section">
        <tui-button
          @click="handleSubmit"
          :disabled="!canSubmit"
          width="100%"
          height="96rpx"
          size="large"
          type="primary"
          shape="circle"
        >
          付费发布 3 元
        </tui-button>
      </view>
    </view>

    <!-- 时间选择器 -->
    <tui-datetime
      ref="startTimePicker"
      :type="1"
      title="选择开始时间"
      :set-date-time="formData.start_time"
      :minutes-data="minutesData"
      @confirm="onStartTimeConfirm"
    />

    <tui-datetime
      ref="endTimePicker"
      :type="1"
      title="选择结束时间"
      :minutes-data="minutesData"
      :set-date-time="formData.end_time"
      @confirm="onEndTimeConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import FormInput from "@/components/common/FormInput.vue";
import { createGig } from "@/api/gig";
import type { CreateGigRequest } from "@/types/gig";
import {
  GIG_SALARY_UNIT_OPTIONS,
  SETTLEMENT_METHOD_OPTIONS,
  GIG_GENDER_OPTIONS,
  GIG_EDUCATION_OPTIONS,
  GIG_EXPERIENCE_OPTIONS,
} from "@/constants/gig";

const formRef = ref();
const agreedToTerms = ref(false);
const startTimePicker = ref();
const endTimePicker = ref();
const minutesData = ["00", "15", "30", "45"];

// --- Picker相关状态 ---
// 1. Picker的选项 (供给picker的range属性)
const salaryUnitPickerOptions = GIG_SALARY_UNIT_OPTIONS.map((o) => o.label);
const settlementPickerOptions = SETTLEMENT_METHOD_OPTIONS.map((o) => o.label);
const genderPickerOptions = GIG_GENDER_OPTIONS.map((o) => o.label);
const educationPickerOptions = GIG_EDUCATION_OPTIONS.map((o) => o.label);
const experiencePickerOptions = GIG_EXPERIENCE_OPTIONS.map((o) => o.label);

// 2. Picker当前选中项的索引 (用于双向绑定picker的value)
const salaryUnitPickerValue = ref(1); // 默认'天'
const settlementPickerValue = ref(0);
const genderPickerValue = ref(0);
const educationPickerValue = ref(0);
const experiencePickerValue = ref(0);

// 3. Picker选中后显示的文本 (计算属性，让template更干净)
const selectedSalaryUnitLabel = computed(
  () => GIG_SALARY_UNIT_OPTIONS[salaryUnitPickerValue.value]?.label
);
const selectedSettlementLabel = computed(
  () => SETTLEMENT_METHOD_OPTIONS[settlementPickerValue.value]?.label
);
const selectedGenderLabel = computed(
  () => GIG_GENDER_OPTIONS[genderPickerValue.value]?.label
);
const selectedEducationLabel = computed(
  () => GIG_EDUCATION_OPTIONS[educationPickerValue.value]?.label
);
const selectedExperienceLabel = computed(
  () => GIG_EXPERIENCE_OPTIONS[experiencePickerValue.value]?.label
);

// --- 表单数据 ---
const initialFormData: CreateGigRequest = {
  title: "",
  start_time: "",
  end_time: "",
  address_name: "",
  address: "",
  detail_address: "",
  latitude: 0,
  longitude: 0,
  people_count: null,
  salary: null,
  salary_unit: GIG_SALARY_UNIT_OPTIONS[salaryUnitPickerValue.value].value,
  settlement: SETTLEMENT_METHOD_OPTIONS[settlementPickerValue.value].value,
  gender: GIG_GENDER_OPTIONS[genderPickerValue.value].value,
  education: GIG_EDUCATION_OPTIONS[educationPickerValue.value].value,
  experience: GIG_EXPERIENCE_OPTIONS[experiencePickerValue.value].value,
  description: "",
  contact_name: "",
  contact_phone: "",
};
const formData = reactive<CreateGigRequest>({ ...initialFormData });

// --- 表单验证规则 ---
const formRules = {
  title: { rule: ["required"], msg: "请输入工作标题" },
  start_time: { rule: ["required"], msg: "请选择开始时间" },
  end_time: { rule: ["required"], msg: "请选择结束时间" },
  address: { rule: ["required"], msg: "请选择工作地点" },
  detail_address: { rule: ["required"], msg: "请输入详细地址" },
  people_count: { rule: ["required"], msg: "请输入招聘人数" },
  salary: { rule: ["required"], msg: "请输入薪酬金额" },
  settlement: { rule: ["required"], msg: "请选择结算方式" },
  contact_name: { rule: ["required"], msg: "请输入联系人" },
  contact_phone: {
    rule: ["required", "isMobile"],
    msg: "请输入正确的联系电话",
  },
};

// --- Picker事件处理 ---
const onSalaryUnitChange = (e: any) => {
  const index = Number(e.detail.value);
  salaryUnitPickerValue.value = index;
  formData.salary_unit = GIG_SALARY_UNIT_OPTIONS[index].value;
};
const onSettlementChange = (e: any) => {
  const index = Number(e.detail.value);
  settlementPickerValue.value = index;
  formData.settlement = SETTLEMENT_METHOD_OPTIONS[index].value;
};
const onGenderChange = (e: any) => {
  const index = Number(e.detail.value);
  genderPickerValue.value = index;
  formData.gender = GIG_GENDER_OPTIONS[index].value;
};
const onEducationChange = (e: any) => {
  const index = Number(e.detail.value);
  educationPickerValue.value = index;
  formData.education = GIG_EDUCATION_OPTIONS[index].value;
};
const onExperienceChange = (e: any) => {
  const index = Number(e.detail.value);
  experiencePickerValue.value = index;
  formData.experience = GIG_EXPERIENCE_OPTIONS[index].value;
};

// --- 时间选择器 ---
const showStartTimePicker = () => startTimePicker.value?.show();
const showEndTimePicker = () => endTimePicker.value?.show();

const onStartTimeConfirm = (e: { result: string }) => {
  const dateTime = e.result;
  if (formData.end_time && new Date(dateTime) >= new Date(formData.end_time)) {
    uni.showToast({ title: "开始时间不能晚于结束时间", icon: "none" });
  } else {
    formData.start_time = dateTime;
  }
};

const onEndTimeConfirm = (e: { result: string }) => {
  const dateTime = e.result;
  if (
    formData.start_time &&
    new Date(dateTime) <= new Date(formData.start_time)
  ) {
    uni.showToast({ title: "结束时间不能早于开始时间", icon: "none" });
  } else {
    formData.end_time = dateTime;
  }
};

// --- 地址选择 ---
const selectLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      formData.address_name = res.name || "";
      formData.address = res.address || "";
      formData.latitude = res.latitude;
      formData.longitude = res.longitude;
    },
    fail: () => uni.showToast({ title: "获取位置失败", icon: "none" }),
  });
};

// --- 其他表单逻辑 ---
const resetForm = () => {
  Object.assign(formData, initialFormData);
  // 重置picker索引
  salaryUnitPickerValue.value = 1;
  settlementPickerValue.value = 0;
  genderPickerValue.value = 0;
  educationPickerValue.value = 0;
  experiencePickerValue.value = 0;
  agreedToTerms.value = false;
};

const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value;
};

const canSubmit = computed(() => {
  const requiredFields: (keyof CreateGigRequest)[] = [
    "title",
    "start_time",
    "end_time",
    "address",
    "detail_address",
    "people_count",
    "salary",
    "settlement",
    "contact_name",
    "contact_phone",
  ];
  return (
    requiredFields.every((field) => !!formData[field]) && agreedToTerms.value
  );
});

// --- 表单提交 ---
const handleSubmit = async () => {
  if (!canSubmit.value) return;

  try {
    await formRef.value.validate();

    // 额外的时间验证
    if (new Date(formData.start_time) >= new Date(formData.end_time)) {
      uni.showToast({ title: "开始时间必须早于结束时间", icon: "none" });
      return;
    }

    // 1. 深拷贝，避免修改原始对象
    const requestData = JSON.parse(JSON.stringify(formData));

    // 2. 将薪酬从元转换为分
    if (requestData.salary) {
      requestData.salary = Math.round(requestData.salary * 100);
    }

    await createGig(requestData);

    uni.showToast({ title: "发布成功！", icon: "success" });

    resetForm();
    setTimeout(() => uni.navigateBack(), 1500);
  } catch (validationError: any) {
    // validationError可能是校验库的错误，也可能是API层的业务错误
    console.error("提交失败:", validationError);
    uni.showToast({
      title: validationError.message || validationError.msg || "请检查表单信息",
      icon: "none",
    });
  }
};

// 开发环境填充测试数据
onMounted(() => {
  if (import.meta.env.DEV) {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const startTime = new Date(tomorrow);
    startTime.setHours(9, 0, 0, 0);
    const endTime = new Date(tomorrow);
    endTime.setHours(18, 0, 0, 0);

    Object.assign(formData, {
      title: "测试零工-咖啡店服务员",
      start_time: startTime.toISOString().slice(0, 16).replace("T", " "),
      end_time: endTime.toISOString().slice(0, 16).replace("T", " "),
      address_name: "星巴克（软件园店）",
      address: "福建省厦门市思明区软件园二期望海路10号",
      detail_address: "一楼大厅",
      latitude: 24.4856,
      longitude: 118.18,
      people_count: 2,
      salary: 25,
      salary_unit: 1, // 小时
      settlement: 1, // 日结
      description:
        "负责点单、制作咖啡、保持店面整洁。要求有责任心，服务态度好。",
      contact_name: "王经理",
      contact_phone: "13800138000",
    });
    // 同步picker的索引
    salaryUnitPickerValue.value = GIG_SALARY_UNIT_OPTIONS.findIndex(
      (o) => o.value === formData.salary_unit
    );
  }
});
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.content {
  margin: var(--spacing-10);
  padding-bottom: 160rpx; /* 为底部操作栏留出空间 */
}

.card {
  margin-bottom: 24rpx;
}

.description-card {
  padding: var(--spacing-16);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  padding: 16rpx;
}

.picker-input {
  height: 100%;
  display: flex;
  align-items: center;
  color: var(--text-base);
  font-size: var(--font-size-base);
  text-align: right;
  width: 100%;
  padding-right: 8rpx;

  &.placeholder {
    color: var(--text-grey);
  }
}

.salary-input-group {
  display: flex;
  align-items: center;
  width: 100%;
}

.salary-input {
  flex: 1;
  font-size: var(--font-size-base);
  color: var(--text-base);
  text-align: right;
  padding-right: 16rpx;
}

.salary-picker {
  flex-shrink: 0;
  .picker-input {
    padding-right: 0;
    border-left: 1rpx solid var(--border-color);
    padding-left: 16rpx;
    min-width: 120rpx; /* 保证最小宽度 */
    justify-content: flex-end; /* 右对齐 */
  }
}

.description-section {
  .description-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-12);

    text {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      color: var(--text-base);
    }

    .char-count {
      font-size: var(--font-size-sm);
      color: var(--text-info);
    }
  }
}

.description-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: var(--spacing-12);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius);
  font-size: var(--font-size-base);
  color: var(--text-base);
  background-color: var(--bg-input);
  line-height: var(--line-height-loose);
  box-sizing: border-box;
}

.textarea-placeholder {
  color: var(--text-grey);
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-8);
  padding: var(--spacing-16) 0;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-card);
  flex-shrink: 0;
  transition: all 0.3s ease;

  &.checked {
    background-color: var(--primary);
    border-color: var(--primary);
    color: var(--text-inverse);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
  }
}

.agreement-text {
  font-size: var(--font-size-base);
  color: var(--text-base);
  line-height: var(--line-height-loose);
  flex: 1;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-12) var(--spacing-16);
  background-color: var(--bg-card);
  border-top: 1rpx solid var(--border-color);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
}
</style>

import type { GigStatus, GigApplicationStatus, GigApprovalMode, GigCheckInMethod } from '@/constants/gig';
import type { PaginatedResponse, PaginatedRequest } from './common';

// ====================================================================
// 核心数据结构 (Core Data Structures)
// 命名规范: 前端统一使用 snake_case, 与后端API保持一致
// ====================================================================

/**
 * 发布者简要信息
 */
export interface PublisherInfo {
  user_id: number;
  nickname: string;
  avatar: string;
}

/**
 * 零工详情信息 (对应 GigDetailResp)
 */
export interface Gig {
  id: number;
  user_id: number;
  title: string;
  description: string;
  salary: number; // 单位: 分
  salary_unit: number;
  settlement: number;
  people_count: number;
  current_people_count: number;
  start_time: string; // ISO 8601 格式字符串
  end_time: string;
  work_duration: number; // 单位: 分钟
  address_name: string;
  address: string;
  detail_address: string;
  full_address: string;
  latitude: number;
  longitude: number;
  gender: number;
  age_min: number;
  age_max: number;
  experience: number;
  education: number;
  skills: string;
  contact_name: string;
  contact_phone: string;
  status: GigStatus;
  approval_mode: GigApprovalMode;
  check_in_method: GigCheckInMethod;
  is_urgent: boolean;
  company_name: string;
  tags: string[];
  images: string[];
  created_at: string; // ISO 8601 格式字符串
  publisher: PublisherInfo;
}

/**
 * 用于申请列表中的零工简要信息
 */
export interface GigInfoForApplication {
  id: number;
  title: string;
  salary: number;
  salary_unit: number;
}

/**
 * 零工申请信息 (对应 GigApplicationResp)
 */
export interface GigApplication {
  id: number;
  gig_id: number;
  user_id: number;
  status: GigApplicationStatus;
  message: string;
  applicant_name: string;
  applicant_phone: string;
  has_experience: boolean;
  experience_description: string;
  reviewed_at?: string;
  reviewer_note: string;
  check_in_at?: string;
  check_out_at?: string;
  created_at: string;
  gig_info?: GigInfoForApplication;
  applicant_info?: PublisherInfo;
}

// ====================================================================
// API 请求/响应体 (API Request/Response Bodies)
// ====================================================================

/**
 * 创建零工请求 (对应 CreateGigReq)
 */
export interface CreateGigRequest {
  title: string;
  description: string;
  salary: number; // 前端传入元, API层转为分
  salary_unit: number;
  settlement: number;
  people_count: number;
  start_time: string;
  end_time: string;
  address_name: string;
  address: string;
  detail_address?: string;
  latitude: number;
  longitude: number;
  gender?: number;
  age_min?: number;
  age_max?: number;
  experience?: number;
  education?: number;
  skills?: string;
  contact_name: string;
  contact_phone: string;
  is_urgent?: boolean;
  company_name?: string;
  tags?: string[];
  images?: string[];
  approval_mode?: GigApprovalMode;
  check_in_method?: GigCheckInMethod;
}

/**
 * 零工列表查询请求 (对应 GigListReq)
 */
export interface ListGigsRequest extends PaginatedRequest {
  keyword?: string;
  salary_min?: number;
  salary_max?: number;
  distance_max?: number; // meters
  user_lat?: number;
  user_lon?: number;
  settlement?: number[];
  experience?: number[];
  education?: number[];
  gender?: number;
  is_urgent?: boolean;
  sort_by?: 'recommend' | 'latest' | 'distance' | 'salary'; // 与后端对齐
  latitude?: number;
  longitude?: number;
}

/**
 * 零工列表项 (增加了距离等前端业务字段)
 */
export type GigListItem = Gig & {
  distance?: number; // 距离, 单位 km
};

/**
 * 零工列表响应
 */
export type ListGigsResponse = PaginatedResponse<GigListItem>;

/**
 * 申请零工请求 (对应 ApplyGigReq)
 */
export interface ApplyGigRequest {
  gig_id: number;
  message?: string;
  applicant_name: string;
  applicant_phone: string;
  has_experience?: boolean;
  experience_description?: string;
}

/**
 * 申请列表查询请求
 */
export interface ListApplicationsRequest extends PaginatedRequest {
  gig_id?: number;
  user_id?: number;
  status?: GigApplicationStatus[];
}

/**
 * 更新申请状态请求 (合并了后端的 ReviewApplicationReq 和 UpdateApplicationStatusReq)
 */
export interface UpdateApplicationStatusRequest {
  application_id: number;
  new_status: GigApplicationStatus;
  reviewer_note?: string;
}

// ====================================================================
// 统计相关类型 (Statistics-related Types)
// ====================================================================

/**
 * 月度统计请求
 */
export interface MonthlyStatsRequest {
  year: number;
  month: number;
}

/**
 * 日期查询请求
 */
export interface DailyGigRequest {
  date: string; // YYYY-MM-DD
}

/**
 * 日期零工数据
 */
export interface DailyGig {
  date: string;
  gig_count: number;
  earnings: number;
  gigs?: Gig[];
  applications?: GigApplication[];
}

/**
 * 月度统计响应
 */
export interface MonthlyStatsResponse {
  year: number;
  month: number;
  total_gigs: number;
  completed_gigs: number;
  total_earnings: number;
  daily_stats: DailyGig[];
}
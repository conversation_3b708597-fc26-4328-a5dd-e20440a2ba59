import { defineConfig, loadEnv } from "vite";
import UnoCSS from 'unocss/vite'
import path from 'node:path'
import uni from "@dcloudio/vite-plugin-uni";
import AutoImport from 'unplugin-auto-import/vite'

// https://vitejs.dev/config/
export default ({ command, mode }) => {
  const env = loadEnv(mode, path.resolve(process.cwd(), './env'), '')

  return defineConfig({
    envDir: "./env", // 自定义env目录
    define: {
      // 确保环境变量可用
      'import.meta.env.VITE_SERVER_BASEURL': JSON.stringify(env.VITE_SERVER_BASEURL || 'http://localhost:8080/api')
    },
    plugins: [
      uni(),
      UnoCSS(),
      //自动引入vue和uni-app的api
      AutoImport({
        imports: ["vue", "uni-app"],
        // 可以选择auto-import.d.ts生成的位置，使用ts建议设置为'src/auto-import.d.ts'
        dts: "src/auto-import.d.ts",
        // 自动生成'eslintrc-auto-import.json'文件，在'.eslintrc.cjs'的'extends'中引入解决报错
        eslintrc: {
          enabled: true,
        },
      }),
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
        "@/static": path.resolve(__dirname, "src/static"),
      },
    },
    // 处理静态资源
    assetsInclude: ['**/*.svg'],
  });
};

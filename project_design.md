# 项目设计文档 (详细版)

## 1. 项目功能摘要

本项目是一个综合性本地生活服务平台，旨在通过集成社区动态、招聘求职、零工市场、同城交友和房产服务等核心功能，构建一个活跃的本地化线上生态系统。平台为用户提供信息发布、浏览、搜索、互动和交易的一站式服务。

### 1.1. 核心模块概览

- **用户与认证模块**: 系统的基础，管理所有用户身份、认证、个人资料和权限。
- **社区动态模块**: 用户生成内容（UGC）的核心，提供社交分享和互动功能。
- **招聘求职模块**: 连接招聘方和求职者，提供专业的招聘服务流程。
- **零工市场模块**: 满足短期、灵活的用工需求，提供任务发布和承接服务。
- **同城交友模块**: 针对性的社交功能，帮助用户建立新的社交关系。
- **房产服务模块**: 涵盖租、售、新房等多种房产信息的发布和检索。
- **消息与通知模块**: 支持用户间的即时通讯和系统消息推送。
- **支付与钱包模块**: 为平台内的付费服务提供支付和资金管理功能。
- **通用与后台模块**: 包括文件上传、后台管理、数据统计等支撑性功能。

## 2. 页面功能及数据字段 (详细版)

### 2.1. 用户与认证模块

- **页面**: `pages/auth/login`, `pages/mine/profile`, `pages/mine/settings`, `pages/dating/profile`
- **功能**: 
    - 微信一键登录、手机号验证码登录。
    - 个人基础信息编辑（昵称、头像、性别、生日、所在地）。
    - 交友资料编辑（身高、体重、职业、学历、兴趣爱好、个人简介、相册）。
    - 账号安全设置（修改手机号、注销账号）。
    - 实名认证。
- **数据字段**:
    - `users` (用户表):
        - `id` (Primary Key)
        - `open_id` (微信OpenID, Unique)
        - `union_id` (微信UnionID, Optional)
        - `phone` (手机号, Unique)
        - `nickname` (昵称)
        - `avatar_url` (头像地址)
        - `gender` (性别: 0-未知, 1-男, 2-女)
        - `birthday` (生日)
        - `city` (常住城市)
        - `status` (账号状态: 0-正常, 1-封禁)
        - `last_login_at` (最后登录时间)
        - `created_at`, `updated_at`
    - `user_profiles` (用户资料表):
        - `user_id` (Foreign Key)
        - `real_name` (真实姓名)
        - `id_card_no` (身份证号)
        - `auth_status` (实名认证状态: 0-未认证, 1-认证中, 2-已认证, 3-认证失败)
    - `dating_profiles` (交友资料表):
        - `user_id` (Foreign Key)
        - `height` (身高, cm)
        - `weight` (体重, kg)
        - `occupation` (职业)
        - `education` (学历)
        - `hobbies` (兴趣爱好, 逗号分隔)
        - `personal_statement` (内心独白)
        - `photo_album` (个人相册, JSON array of image URLs)

### 2.2. 社区动态模块 (`pages/post`)

- **功能**: 发布图文动态、话题关联、点赞、评论、回复、分享、举报。
- **数据字段**:
    - `posts` (动态表):
        - `id` (PK)
        - `user_id` (FK)
        - `content` (文本内容, Text)
        - `images` (图片, JSON array)
        - `video_url` (视频地址, Optional)
        - `topic_id` (关联话题ID, FK, Optional)
        - `location` (发布位置)
        - `view_count` (浏览量)
        - `status` (状态: 0-审核中, 1-通过, 2-不通过, 3-用户删除)
    - `topics` (话题表): `id`, `name`, `description`
    - `likes` (点赞表): `id`, `user_id`, `target_id` (动态/评论ID), `target_type` (post/comment)
    - `comments` (评论表): `id`, `post_id`, `user_id`, `content`, `parent_id` (用于回复)
    - `reports` (举报表): `id`, `user_id`, `target_id`, `target_type`, `reason`, `status`

### 2.3. 招聘求职模块 (`pages/job`)

- **功能**: 招聘者/求职者角色切换、职位发布/管理、简历创建/管理、求职意向、企业认证、在线沟通、职位/人才搜索和筛选。
- **数据字段**:
    - `companies` (公司表): `id`, `user_id` (创建人), `name`, `logo`, `description`, `industry`, `scale`, `address`, `auth_status`
    - `jobs` (职位表):
        - `id` (PK)
        - `company_id` (FK)
        - `recruiter_id` (发布人ID, FK to users)
        - `title` (职位名称)
        - `category` (职位类别)
        - `job_type` (工作类型: 全职/兼职/实习)
        - `description` (职位描述)
        - `requirements` (任职要求)
        - `salary_min`, `salary_max`
        - `experience_required` (经验要求)
        - `education_required` (学历要求)
        - `address`, `location` (经纬度)
        - `status` (0-招聘中, 1-已关闭)
    - `resumes` (简历表): `id`, `user_id`, `title`, `content` (富文本或JSON结构化数据), `is_default`
    - `job_applications` (投递记录表): `id`, `job_id`, `resume_id`, `user_id`, `status` (0-已投递, 1-被查看, 2-约面试, 3-不合适)

### 2.4. 房产服务模块 (`pages/house`)

- **功能**: 新房/二手房/租房/商铺信息发布与管理、条件筛选（区域、价格、户型、面积等）、收藏、联系房东/中介。
- **数据字段**:
    - `houses` (房产主表):
        - `id` (PK)
        - `publisher_id` (发布人ID, FK to users)
        - `house_type` (房源类型: new, secondhand, rent, commercial)
        - `title` (标题)
        - `city`, `district`, `address`
        - `location` (经纬度)
        - `description`
        - `images` (JSON array)
        - `contact_person`, `contact_phone`
        - `status` (0-待售/租, 1-已售/租, 2-下架)
    - `house_rent_details` (租房详情): `house_id`, `rent_price`, `payment_method` (押一付三), `layout` (2室1厅), `area`, `floor`, `total_floors`, `orientation`, `facilities` (JSON array)
    - `house_secondhand_details` (二手房详情): `house_id`, `sell_price`, `layout`, `area`, `floor`, `total_floors`, `orientation`, `year_built`, `ownership`
    - `house_new_details` (新房详情): `house_id`, `developer`, `opening_date`, `avg_price`
    - `collections` (收藏表): `id`, `user_id`, `target_id`, `target_type` (job/house/post...)

## 3. 后端服务架构设计 (详细版)

### 3.1. 技术栈

- **语言**: Golang
- **Web框架**: Gin
- **ORM**: GORM
- **数据库**: PostgreSQL
- **缓存**: Redis (用于缓存热点数据、Session、验证码)
- **消息队列**: RabbitMQ (用于异步任务，如发邮件、消息推送、日志处理)
- **支付**: 微信支付 / 支付宝支付
- **定时任务**: Go-Cron (用于定时清理数据、生成报表等)
- **配置管理**: Viper
- **日志**: Zap
- **容器化**: Docker, Docker Compose

### 3.2. 增强版后端目录结构

这是一个更具扩展性的分层结构，适用于中大型项目。

```
/fnbdb-mini-backend
├── api/                # API层: 协议适配 (HTTP, gRPC)
│   └── http/
│       ├── handler/    # Gin Handlers (Controller)
│       │   ├── user.go
│       │   ├── post.go
│       │   └── ...
│       ├── middleware/ # Gin 中间件 (Auth, Logging, CORS)
│       │   ├── auth.go
│       │   └── logger.go
│       ├── request/    # 请求体验证结构体
│       │   └── user_req.go
│       ├── response/   # 响应体封装结构体
│       │   └── user_res.go
│       └── router.go   # 路由定义
├── cmd/                # 程序入口
│   └── api/
│       └── main.go
├── config/             # 配置文件 (config.yaml)
│   └── config.go
├── internal/           # 核心业务逻辑 (不对外暴露)
│   ├── biz/            # Business Logic (业务逻辑层, Service)
│   │   ├── user_biz.go
│   │   ├── post_biz.go
│   │   └── ...
│   ├── data/           # Data Access (数据访问层, Repository)
│   │   ├── model/      # 数据库模型 (GORM structs)
│   │   │   └── user.go
│   │   ├── user_repo.go
│   │   ├── post_repo.go
│   │   └── data.go     # 数据库和Redis客户端初始化
│   └── service/        # Service层: 组装biz, 对handler暴露
│       ├── user_svc.go
│       └── ...
├── pkg/                # 第三方或内部公共库
│   ├── auth/           # JWT, Casbin等
│   ├── cache/          # Redis 客户端封装
│   ├── log/            # Zap 日志封装
│   ├── mq/             # RabbitMQ 客户端封装
│   ├── payment/        # 支付SDK封装
│   ├── scheduler/      # 定时任务
│   └── utils/          # 通用工具
├── scripts/            # 脚本 (数据库迁移, ...)
├── storage/            # 本地文件存储 (上传等)
├── test/               # 测试文件
├── .gitignore
├── Dockerfile
├── docker-compose.yml
├── go.mod
└── README.md
```

### 3.3. 目录结构说明

- **`api/`**: 负责与外部的协议交互。`handler`处理请求，`request`/`response`定义数据结构，`middleware`处理公共逻辑，`router`组织路由。
- **`internal/`**: 项目的核心。
    - **`biz/` (Business Logic)**: 纯粹的业务逻辑层，不依赖任何具体的协议（如HTTP）或数据源实现。它定义了业务操作的接口和实现，并调用`data`层进行数据持久化。
    - **`data/` (Data Access)**: 数据访问层，负责数据库、缓存等数据源的交互。`repository`的具体实现放在这里。
    - **`service/`**: 服务层，它像是`biz`和`handler`之间的适配器。它将一个或多个`biz`的操作组装成一个完整的服务，供`handler`调用。这种结构在业务变得复杂时能提供更好的隔离性。
- **`cmd/`**: 应用的启动入口，负责组装所有依赖（DI，依赖注入）。
- **`pkg/`**: 高度可复用的公共代码库，可以被外部项目引用。
- **`scripts/`**: 存放各种脚本，如数据库迁移、数据填充等。
- **`storage/`**: 用于存放本地生成或上传的文件。
- **`test/`**: 存放集成测试和端到端测试。
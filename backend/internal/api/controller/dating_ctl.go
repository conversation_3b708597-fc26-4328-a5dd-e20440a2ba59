package controller

import (
	"bdb-backend/pkg/response"
	"net/http"

	"github.com/gin-gonic/gin"
)

type DatingController struct {
	// datingService service.DatingService
}

func NewDatingController() *DatingController {
	return &DatingController{}
}

// GetProfiles 获取交友用户列表
func (c *DatingController) GetProfiles(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, response.Success(gin.H{
		"message": "获取交友用户列表",
	}))
}

// LikeProfile 点赞用户
func (c *DatingController) LikeProfile(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, response.Success(gin.H{
		"message": "点赞用户",
	}))
}

// GetMatches 获取匹配列表
func (c *DatingController) GetMatches(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, response.Success(gin.H{
		"message": "获取匹配列表",
	}))
}

package controller

import (
	"strconv"

	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/storage"
	"time"

	"github.com/gin-gonic/gin"
)

// CommonController ...
type CommonController struct {
	storage storage.Storage
}

// NewCommonController creates a new CommonController
func NewCommonController(storage storage.Storage) *CommonController {
	return &CommonController{
		storage: storage,
	}
}

func (c *CommonController) GetUploadToken(ctx *gin.Context) {
	// For now, key is not used in qiniu implementation, let frontend decide the key
	token, err := c.storage.GenerateUploadToken("", time.Hour)
	if err != nil {
		logger.ErrorCtx(ctx, "failed to generate upload token", err)
		response.ServerError(ctx, "Failed to generate upload token")
		return
	}

	qiniuStorage, ok := c.storage.(*storage.QiniuStorage)
	if !ok {
		logger.ErrorCtx(ctx, "storage service is not qiniu storage", nil)
		response.ServerError(ctx, "incorrect storage service configuration")
		return
	}

	resp := &types.UploadTokenResponse{
		Token:  token,
		Domain: qiniuStorage.GetDomain(),
	}
	response.OK(ctx, resp)
}

// GetUserID ...
func GetUserID(ctx *gin.Context) uint {
	val, ok := ctx.Get("user_id")
	if !ok {
		logger.ErrorCtx(ctx, "user_id not found in context", nil)
		response.Unauthorized(ctx, "user_id not found in context")
		return 0
	}

	// 支持多种类型的转换以保持兼容性
	switch v := val.(type) {
	case uint:
		return v
	case uint64:
		return uint(v)
	case int64:
		if v < 0 {
			response.Unauthorized(ctx, "无效的用户ID")
			return 0
		}
		return uint(v)
	case int:
		if v < 0 {
			response.Unauthorized(ctx, "无效的用户ID")
			return 0
		}
		return uint(v)
	case float64:
		if v < 0 {
			response.Unauthorized(ctx, "无效的用户ID")
			return 0
		}
		return uint(v)
	default:
		logger.ErrorCtx(ctx, "user_id type conversion failed", nil, "type", v)
		response.Unauthorized(ctx, "用户ID类型错误")
		return 0
	}
}

// ParseUintParam 解析URL参数为uint类型
func ParseUintParam(ctx *gin.Context, paramName string) uint {
	paramStr := ctx.Param(paramName)
	paramVal, err := strconv.ParseUint(paramStr, 10, 32)
	if err != nil {
		response.BadRequest(ctx, "无效的参数: "+paramName)
		return 0
	}
	return uint(paramVal)
}

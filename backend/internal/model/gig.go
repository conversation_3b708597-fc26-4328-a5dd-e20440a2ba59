package model

import (
	"bdb-backend/internal/constants"
	"fmt"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// Gig represents a short-term job posting.
type Gig struct {
	BaseModel
	UserID             uint                `gorm:"not null;comment:发布者ID" json:"user_id"`
	Title              string              `gorm:"size:100;not null;comment:工作标题" json:"title"`
	Description        string              `gorm:"type:text;not null;comment:工作描述" json:"description"`
	Salary             int                 `gorm:"not null;comment:薪酬(分)" json:"salary"`
	SalaryUnit         int                 `gorm:"not null;comment:薪酬单位: 1-小时, 2-天, 3-件, 4-总价" json:"salary_unit"`
	Settlement         int                 `gorm:"not null;comment:结算方式: 1-日结, 2-周结, 3-月结, 4-完工结" json:"settlement"`
	PeopleCount        int                 `gorm:"not null;comment:招聘人数" json:"people_count"`
	CurrentPeopleCount int                 `gorm:"default:0;comment:当前报名人数" json:"current_people_count"`
	StartTime          time.Time           `gorm:"not null;comment:开始时间" json:"start_time"`
	EndTime            time.Time           `gorm:"not null;comment:结束时间" json:"end_time"`
	WorkDuration       int                 `gorm:"comment:工作时长(分钟，后端计算生成)" json:"work_duration"`
	ExpireTime         time.Time           `gorm:"not null;comment:自动关闭时间(end_time + 10min)" json:"expire_time"`
	AddressName        string              `gorm:"size:255;comment:位置简称(uni.chooseLocation的res.name)" json:"address_name"`
	Address            string              `gorm:"size:255;not null;comment:具体地址(uni.chooseLocation的res.address)" json:"address"`
	DetailAddress      string              `gorm:"size:255;comment:详细地址(用户输入的楼层门牌号等)" json:"detail_address"`
	FullAddress        string              `gorm:"size:500;not null;comment:完整地址(address + detail_address)" json:"full_address"`
	Location           string              `gorm:"type:geography(Point,4326);comment:工作地点PostGIS坐标点" json:"location"`
	Latitude           float64             `gorm:"type:decimal(10,7);comment:纬度" json:"latitude"`
	Longitude          float64             `gorm:"type:decimal(10,7);comment:经度" json:"longitude"`
	Gender             int                 `gorm:"not null;comment:性别要求: 0-不限, 1-男, 2-女" json:"gender"`
	AgeMin             int                 `gorm:"not null;default:18;comment:最小年龄" json:"age_min"`
	AgeMax             int                 `gorm:"not null;default:60;comment:最大年龄" json:"age_max"`
	Experience         int                 `gorm:"not null;comment:经验要求: 0-不限, 1-应届生, ..." json:"experience"`
	Education          int                 `gorm:"not null;comment:学历要求: 0-不限, 1-初中, ..." json:"education"`
	Skills             string              `gorm:"type:text;not null;comment:技能要求" json:"skills"`
	ContactName        string              `gorm:"size:50;not null;comment:联系人姓名" json:"contact_name"`
	ContactPhone       string              `gorm:"size:20;not null;comment:联系电话" json:"contact_phone"`
	Status             constants.GigStatus `gorm:"type:varchar(20);not null;comment:状态" json:"status"`
	ApprovalMode       string              `gorm:"type:varchar(20);not null;default:'manual';comment:审批模式: manual-手动, auto-自动录用" json:"approval_mode"`
	CheckInMethod      string              `gorm:"type:varchar(20);not null;default:'none';comment:打卡方式: none-无, gps-GPS, qrcode-二维码" json:"check_in_method"`
	CloseReason        string              `gorm:"size:100;comment:关闭原因: expired, manual, filled" json:"close_reason"`
	Priority           int                 `gorm:"comment:优先级" json:"priority"`
	IsUrgent           bool                `gorm:"default:false;comment:是否紧急" json:"is_urgent"`
	CompanyName        string              `gorm:"size:100;comment:公司名称" json:"company_name"`
	Tags               datatypes.JSON      `gorm:"comment:标签(JSON数组)" json:"tags"`
	Images             datatypes.JSON      `gorm:"comment:图片(JSON数组)" json:"images"`
}

// TableName returns the table name for the Gig model.
func (Gig) TableName() string {
	return "gigs"
}

// BeforeCreate GORM钩子，在创建前设置Location字段为PostGIS格式
func (g *Gig) BeforeCreate(tx *gorm.DB) error {
	return g.updateLocationPoint(tx)
}

// BeforeUpdate GORM钩子，在更新前设置Location字段为PostGIS格式
func (g *Gig) BeforeUpdate(tx *gorm.DB) error {
	return g.updateLocationPoint(tx)
}

// updateLocationPoint 根据经纬度更新Location字段为PostGIS格式
func (g *Gig) updateLocationPoint(_ *gorm.DB) error {
	if g.Latitude != 0 && g.Longitude != 0 {
		// 使用PostGIS的POINT格式，WKT (Well-Known Text)格式
		g.Location = fmt.Sprintf("POINT(%f %f)", g.Longitude, g.Latitude)
	}
	return nil
}

// GigApplication 零工申请模型
type GigApplication struct {
	BaseModel
	GigID                 uint                           `gorm:"not null;comment:零工ID" json:"gig_id"`
	UserID                uint                           `gorm:"not null;comment:申请者ID" json:"user_id"`
	Status                constants.GigApplicationStatus `gorm:"type:varchar(20);default:'Pending';comment:状态" json:"status"`
	Message               string                         `gorm:"size:500;not null;comment:申请留言" json:"message"`
	ApplicantName         string                         `gorm:"size:50;comment:申请者姓名" json:"applicant_name"`
	ApplicantPhone        string                         `gorm:"size:20;comment:申请者联系电话" json:"applicant_phone"`
	HasExperience         bool                           `gorm:"default:false;comment:是否有相关经验" json:"has_experience"`
	ExperienceDescription string                         `gorm:"size:255;comment:经验简述" json:"experience_description"`
	ReviewedAt            *time.Time                     `gorm:"comment:审核时间" json:"reviewed_at"`
	ReviewerNote          string                         `gorm:"type:text;comment:审核备注" json:"reviewer_note"`
	CheckInAt             *time.Time                     `gorm:"comment:上班打卡时间" json:"check_in_at"`
	CheckOutAt            *time.Time                     `gorm:"comment:下班打卡时间" json:"check_out_at"`
}

// TableName returns the table name for the GigApplication model.
func (GigApplication) TableName() string {
	return "gig_applications"
}

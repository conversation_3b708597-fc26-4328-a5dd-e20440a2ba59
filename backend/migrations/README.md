# 聊天系统数据库迁移指南

## 概述

本目录包含聊天与通知系统的数据库迁移文件，基于设计方案 V2.0 实现。

## 文件说明

- `schema.sql` - 完整的数据库表结构定义
- `README.md` - 本说明文档

## 设计亮点

### 1. 🚀 核心架构优势

- **读写分离**：频繁变化的数据（如未读数）通过缓存计算，避免数据库热点更新
- **数据一致性**：通过动态计算保证数据强一致性
- **高性能索引**：精心设计的索引支持高效查询
- **扩展性强**：支持私聊和系统通知，易于扩展群聊功能

### 2. 📊 表结构设计

| 表名 | 用途 | 特点 |
|------|------|------|
| `conversations` | 会话基础信息 | 使用user_id1/user_id2经典设计，稳定存储 |
| `user_conversations` | 用户会话状态 | 包含已读状态，减少表数量 |
| `messages` | 消息内容 | 核心消息数据，支持多种类型 |

### 3. 🎯 性能优化

- **复合索引**：针对查询模式优化的复合索引
- **分区支持**：消息表支持按时间分区（可选）
- **触发器**：自动更新时间戳
- **JSON字段**：使用JSONB存储扩展信息

## 执行迁移

### 1. 连接数据库

```bash
# 连接PostgreSQL数据库
psql -h localhost -U username -d database_name
```

### 2. 执行迁移

```sql
-- 执行完整的schema创建
\i migrations/schema.sql
```

### 3. 验证结果

```sql
-- 查看创建的表
\dt

-- 查看表结构
\d conversations
\d user_conversations
\d messages
\d user_read_status
\d message_receipts

-- 查看索引
\di
```

## 数据模型关系

```
User (existing)
├── UserConversation (1:N) - 用户的会话状态（包含已读信息）
└── Message (1:N) - 用户发送的消息

Conversation
├── UserConversation (1:N) - 会话的参与用户
└── Message (1:N) - 会话的消息列表

Message
└── UserConversation (N:1) - 关联用户的已读状态
```

## 核心业务流程

### 1. 发送消息

```sql
-- 1. 创建/获取会话（确保user_id1 < user_id2）
INSERT INTO conversations (type, user_id1, user_id2, created_by) 
VALUES ('single', 1, 2, 1) 
ON CONFLICT (user_id1, user_id2, type) DO NOTHING;

-- 2. 插入消息
INSERT INTO messages (msg_id, conversation_id, sender_id, content, message_type)
VALUES ('uuid-here', 1, 1, 'Hello', 'text');

-- 3. 更新用户会话状态
UPDATE user_conversations 
SET last_active_time = NOW() 
WHERE user_id = 1 AND conversation_id = 1;
```

### 2. 获取会话列表

```sql
-- 查询用户的会话列表（包含动态计算的未读数）
SELECT 
    uc.*,
    c.*,
    (SELECT content FROM messages WHERE conversation_id = c.id ORDER BY created_at DESC LIMIT 1) as last_message_content,
    (SELECT COUNT(*) FROM messages m 
     WHERE m.conversation_id = c.id 
     AND m.sender_id != uc.user_id 
     AND m.id > COALESCE(uc.last_read_message_id, 0)
    ) as unread_count
FROM user_conversations uc
JOIN conversations c ON uc.conversation_id = c.id
WHERE uc.user_id = 1 AND uc.is_visible = true
ORDER BY uc.is_pinned DESC, uc.last_active_time DESC;
```

### 3. 标记已读

```sql
-- 更新用户会话的已读状态
UPDATE user_conversations 
SET 
    last_read_message_id = 999,
    last_read_time = NOW(),
    updated_at = NOW()
WHERE user_id = 1 AND conversation_id = 1;

-- 更新消息状态为已读
UPDATE messages 
SET status = 'read', read_at = NOW()
WHERE conversation_id = 1 
AND sender_id != 1 
AND id > (前一个已读消息ID);
```

## 缓存策略

### Redis缓存设计

```redis
# 用户会话列表（有序集合，按活跃时间排序）
ZADD user_conversations:123 1672531200 "conv_456"

# 会话最新消息缓存
HSET conv_last_message:456 
    "id" "999"
    "msg_id" "uuid-here"
    "content" "Hello World"
    "sender_id" "100"

# 用户未读数缓存
HSET user_unread_count:123
    "conv_456" "5"
    "conv_789" "0"

# 用户已读状态缓存
HSET user_read_status:123
    "conv_456" "999"
    "conv_789" "888"
```

## 性能监控

### 关键指标

1. **查询性能**
   - 会话列表查询时间 < 10ms
   - 消息历史查询时间 < 50ms
   - 发送消息处理时间 < 100ms

2. **索引使用率**
   - 确保查询都使用到合适的索引
   - 监控慢查询日志

3. **数据增长**
   - 消息表数据量增长趋势
   - 考虑分区策略

### 监控SQL

```sql
-- 查看表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 查看索引使用情况
SELECT 
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
```

## 注意事项

### 1. 数据一致性

- 消息发送使用事务确保一致性
- 已读状态更新考虑并发场景
- 定期检查缓存与数据库数据一致性

### 2. 性能优化

- 大数据量时考虑消息表分区
- 定期清理过期的消息回执数据
- 监控索引性能，必要时调整

### 3. 扩展性

- 预留群聊功能扩展空间
- 支持消息类型扩展
- 考虑跨库查询场景

## 版本历史

- **V2.0** (2024-01-01): 基于新设计方案的完整实现
  - 移除冗余存储，改为动态计算
  - 独立已读状态表
  - 消息UUID机制
  - 完善的索引设计 
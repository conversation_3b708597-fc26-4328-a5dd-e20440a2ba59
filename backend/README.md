# BDB Backend Service

本项目是一个基于Go语言的后端服务，使用Gin框架构建。

## 主要功能

- 用户认证与管理
- 聊天室与消息系统  
- 职位发布与管理
- 房源信息管理
- 零工信息发布
- 交友功能
- 支付集成

## 最近更新

### JSON响应统一封装 (2024)

为了提高代码的可维护性和一致性，我们对JSON响应进行了统一封装：

#### 特性
- **统一响应格式**: 所有API接口使用相同的JSON响应结构
- **简洁API**: 提供`Success`、`Error`等基础方法
- **分页支持**: 内置分页响应格式`Paginated`
- **状态码管理**: 统一的状态码和消息常量定义

#### 使用示例
```go
// 成功响应
ctx.JSON(http.StatusOK, response.Success(data))

// 错误响应
ctx.JSON(http.StatusInternalServerError, response.Error(response.CodeServerError, "服务器内部错误"))

// 分页响应
ctx.JSON(http.StatusOK, response.Paginated(items, total, page, pageSize))

// 便捷方法
response.OK(ctx, data)    // 成功响应
response.Fail(ctx, 400, "参数错误")  // 错误响应
```

### 日志系统改进 (2024)

简化并改进了日志系统，提供实用的日志记录功能：

#### 特性
- **基础日志方法**: `Debug`、`Info`、`Warn`、`Error`、`Fatal`
- **上下文日志**: `InfoCtx`、`ErrorCtx`支持带上下文的日志记录
- **日志文件格式**: 按日期生成，格式为`app-2025-03-19.log`
- **结构化参数**: 支持key-value对的结构化日志

#### 使用示例
```go
// 基础日志
logger.Info("User login successful")

// 带参数的日志
logger.Info("Processing request", "user_id", userID, "action", "login")

// 带上下文的日志
logger.InfoCtx(ctx, "User created", "user_id", user.ID, "nickname", user.Nickname)

// 错误日志
logger.ErrorCtx(ctx, "Database operation failed", err, "table", "users")
```

#### 日志文件
- 文件格式：`app-YYYY-MM-DD.log`（如：`app-2025-03-19.log`）
- 位置：`./logs/` 目录
- 自动按日期轮转

## 技术栈

- **语言**: Go 1.21+
- **框架**: Gin
- **数据库**: PostgreSQL
- **缓存**: Redis
- **消息队列**: Centrifugo
- **依赖注入**: Google Wire
- **日志**: Zerolog
- **配置管理**: Viper


## 开发规范

### 编程规范
本项目遵循基于 Uber Go Style Guide 的现代化编程规范：

📖 **[Go 编程规范 (Go 1.24)](../.cursor/rules/09-golang-coding-standards.mdc)**
🛠️ **[Go 开发工具和流程](../.cursor/rules/10-golang-development-tools.mdc)**

#### 核心原则
- **可读性优先** - 代码是给人读的
- **错误处理明确** - 每个错误都要有明确的处理策略
- **并发安全** - 正确使用 Go 的并发特性
- **性能考虑** - 避免常见的性能陷阱
- **一致性** - 统一的编码风格

#### 代码质量检查
```bash
# 格式化代码
make fmt

# 代码检查
make vet

# 静态分析
make staticcheck

# 完整质量检查
make check-all
```

### 响应格式
所有API接口返回统一的JSON格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 日志记录规范
- 使用结构化日志，支持key-value对
- 关键业务操作必须记录日志
- 错误处理时记录详细错误信息

## 快速开始

1. 克隆项目
   ```bash
   git clone <repository-url>
cd backend
   ```

2. 安装依赖
   ```bash
   go mod download
   ```

3. 配置环境
   ```bash
cp configs/config.dev.yaml configs/config.yaml
# 编辑配置文件
   ```

4. 运行项目
   ```bash
# 开发模式
   make dev

# 生产模式
make build && ./bin/server
   ```

## API文档

启动服务后，访问 `http://localhost:8080/swagger/index.html` 查看API文档。

## 测试

```bash
# 运行所有测试
make test

# 运行单元测试
make test-unit

# 运行集成测试  
make test-integration
```

## 部署

使用Docker部署：
```bash
# 构建镜像
make docker-build

# 运行容器
make docker-run
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交变更
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License 
# 本地宝 (fnbdb-mini) 项目分析与后端架构设计

## 📋 项目概述

本地宝是一个基于 UniApp/Vue 3 的综合性本地服务平台，提供求职招聘、房产交易、交友社交、零工服务等多种本地生活服务。项目采用现代化的前端技术栈，支持 H5、微信小程序、App 等多端部署。

### 技术栈
- **前端框架**: UniApp + Vue 3 + TypeScript
- **UI组件**: uv-ui + ThorUI + uni-ui
- **状态管理**: Pinia + 持久化存储
- **样式方案**: UnoCSS + SCSS
- **构建工具**: Vite
- **图标系统**: Carbon Icons + Solar Icons

## 🏗️ 功能模块分析

### 1. 核心导航模块
- **求职招聘**: 双角色系统（求职者/招聘者）
- **房产服务**: 二手房、租房、新房、商业地产
- **交友社交**: 匹配系统、动态广场、私信聊天
- **零工服务**: 任务发布、接单管理、日历系统
- **社区动态**: 内容发布、互动评论、话题系统

### 2. 用户管理模块
- **身份认证**: 手机登录、微信授权
- **个人资料**: 头像、简历、认证状态
- **钱包系统**: 余额管理、充值提现、积分系统
- **消息中心**: 系统通知、私信聊天、消息设置

### 3. 内容管理模块
- **发布管理**: 多类型内容发布（职位、房源、动态）
- **收藏系统**: 个人收藏、历史浏览
- **搜索功能**: 全文搜索、筛选排序
- **地理定位**: 城市切换、位置服务

## 🎯 数据结构分析

### 用户相关数据结构

```typescript
interface UserInfo {
  id: string
  name: string
  avatar?: string
  phone?: string
  wechat?: string
  email?: string
  location?: string
  isVip?: boolean
  isVerified?: boolean
  verificationLevel?: 'none' | 'phone' | 'identity' | 'enterprise'
  balance?: number
  points?: number
  joinDate?: string
}
```

### 房产相关数据结构

```typescript
interface BaseHouseItem {
  id: string
  title?: string
  image: string
  images?: string[]
  tags?: string[]
  area?: string | number
  price?: string | number
  hasVideo?: boolean
  hasVR?: boolean
}

interface RentHouseItem extends BaseHouseItem {
  layout?: string
  rooms?: number
  halls?: number
  direction?: string
  floor?: string
  decoration?: string
  rentType?: string
  paymentMethod?: string
  checkInTime?: string
  contactPerson?: string
  contactPhone?: string
  furniture?: string[]
}
```

### 职位相关数据结构

```typescript
interface JobItem {
  id: string
  title: string
  salary: string
  companyName: string
  industry: string
  tags: string[]
  area: string
  publishTime: string
  isUrgent?: boolean
  experience?: string
  education?: string
  jobType?: string
  welfare?: string[]
}
```

## 🏛️ 后端架构设计

### 技术栈选择
- **核心框架**: Golang + Gin
- **数据库**: PostgreSQL (主库) + Redis (缓存)
- **ORM**: GORM
- **消息队列**: RabbitMQ
- **文件存储**: 七牛云/阿里云OSS
- **支付集成**: 微信支付
- **认证方式**: JWT + 微信授权

### 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │   Load Balancer │    │   CDN/Static    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Auth Service   │    │  User Service   │    │  File Service   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Job Service    │    │ House Service   │    │ Dating Service  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Post Service   │    │Message Service  │    │Payment Service  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │   RabbitMQ      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 目录结构设计

```
backend/
├── cmd/
│   └── server/
│       └── main.go                 # 应用入口
├── internal/
│   ├── config/                     # 配置管理
│   │   ├── config.go
│   │   └── database.go
│   ├── handler/                    # HTTP处理器
│   │   ├── auth/
│   │   ├── user/
│   │   ├── job/
│   │   ├── house/
│   │   ├── dating/
│   │   ├── post/
│   │   └── common/
│   ├── service/                    # 业务逻辑层
│   │   ├── auth/
│   │   ├── user/
│   │   ├── job/
│   │   ├── house/
│   │   ├── dating/
│   │   ├── post/
│   │   └── payment/
│   ├── repository/                 # 数据访问层
│   │   ├── user/
│   │   ├── job/
│   │   ├── house/
│   │   ├── dating/
│   │   └── post/
│   ├── model/                      # 数据模型
│   │   ├── user.go
│   │   ├── job.go
│   │   ├── house.go
│   │   ├── dating.go
│   │   └── post.go
│   ├── middleware/                 # 中间件
│   │   ├── auth.go
│   │   ├── cors.go
│   │   ├── logger.go
│   │   └── rate_limit.go
│   └── utils/                      # 工具函数
│       ├── jwt.go
│       ├── validator.go
│       ├── upload.go
│       └── wechat.go
├── pkg/                           # 公共包
│   ├── database/
│   ├── redis/
│   ├── rabbitmq/
│   └── logger/
├── migrations/                    # 数据库迁移
├── scripts/                       # 部署脚本
├── docker/                        # Docker配置
├── docs/                          # API文档
├── go.mod
└── go.sum
```

## 🗄️ 数据库设计

### 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone VARCHAR(20) UNIQUE NOT NULL,
    wechat_openid VARCHAR(100),
    name VARCHAR(100) NOT NULL,
    avatar TEXT,
    gender SMALLINT DEFAULT 0, -- 0:未知 1:男 2:女
    birth_date DATE,
    location VARCHAR(200),
    is_vip BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_level VARCHAR(20) DEFAULT 'none',
    balance DECIMAL(10,2) DEFAULT 0,
    points INTEGER DEFAULT 0,
    status SMALLINT DEFAULT 1, -- 1:正常 2:禁用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 职位表 (jobs)
```sql
CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    salary_min INTEGER,
    salary_max INTEGER,
    salary_text VARCHAR(100),
    company_name VARCHAR(200),
    industry VARCHAR(100),
    job_type VARCHAR(50), -- 全职/兼职/实习
    experience VARCHAR(50),
    education VARCHAR(50),
    location VARCHAR(200),
    tags TEXT[], -- PostgreSQL数组类型
    welfare TEXT[],
    is_urgent BOOLEAN DEFAULT FALSE,
    status SMALLINT DEFAULT 1, -- 1:招聘中 2:暂停 3:结束
    view_count INTEGER DEFAULT 0,
    apply_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 房源表 (houses)
```sql
CREATE TABLE houses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    type VARCHAR(20) NOT NULL, -- rent/second_hand/new/commercial
    title VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(12,2),
    unit_price DECIMAL(10,2),
    area DECIMAL(8,2),
    rooms INTEGER,
    halls INTEGER,
    bathrooms INTEGER,
    floor VARCHAR(20),
    total_floors INTEGER,
    direction VARCHAR(20),
    decoration VARCHAR(50),
    build_year INTEGER,
    community VARCHAR(200),
    address VARCHAR(500),
    location_lat DECIMAL(10,8),
    location_lng DECIMAL(11,8),
    images TEXT[],
    tags TEXT[],
    has_vr BOOLEAN DEFAULT FALSE,
    has_video BOOLEAN DEFAULT FALSE,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    status SMALLINT DEFAULT 1,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔌 API 端点设计

### 认证相关 API
```
POST   /api/v1/auth/login          # 手机号登录
POST   /api/v1/auth/wechat         # 微信授权登录
POST   /api/v1/auth/refresh        # 刷新Token
POST   /api/v1/auth/logout         # 退出登录
```

### 用户相关 API
```
GET    /api/v1/user/profile        # 获取个人资料
PUT    /api/v1/user/profile        # 更新个人资料
POST   /api/v1/user/avatar         # 上传头像
GET    /api/v1/user/wallet         # 钱包信息
POST   /api/v1/user/recharge       # 充值
POST   /api/v1/user/withdraw       # 提现
```

### 职位相关 API
```
GET    /api/v1/jobs                # 职位列表
POST   /api/v1/jobs                # 发布职位
GET    /api/v1/jobs/:id            # 职位详情
PUT    /api/v1/jobs/:id            # 更新职位
DELETE /api/v1/jobs/:id            # 删除职位
POST   /api/v1/jobs/:id/apply      # 申请职位
GET    /api/v1/jobs/my             # 我的职位
```

### 房源相关 API
```
GET    /api/v1/houses              # 房源列表
POST   /api/v1/houses              # 发布房源
GET    /api/v1/houses/:id          # 房源详情
PUT    /api/v1/houses/:id          # 更新房源
DELETE /api/v1/houses/:id          # 删除房源
GET    /api/v1/houses/my           # 我的房源
POST   /api/v1/houses/:id/favorite # 收藏房源
```

## 🚀 部署架构

### 容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
      - rabbitmq
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: fnbdb
      POSTGRES_USER: fnbdb
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: fnbdb
      RABBITMQ_DEFAULT_PASS: password
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
```

## 📈 扩展性考虑

### 1. 微服务拆分
- 用户服务 (User Service)
- 职位服务 (Job Service)  
- 房产服务 (House Service)
- 交友服务 (Dating Service)
- 消息服务 (Message Service)
- 支付服务 (Payment Service)

### 2. 缓存策略
- Redis 缓存热点数据
- 分布式缓存一致性
- 缓存预热和更新策略

### 3. 搜索优化
- Elasticsearch 全文搜索
- 地理位置搜索优化
- 搜索结果个性化推荐

### 4. 实时功能
- WebSocket 实时消息
- 服务器推送通知
- 实时数据同步

## 🔒 安全考虑

1. **身份认证**: JWT + 微信OAuth
2. **数据加密**: HTTPS + 敏感数据加密存储
3. **访问控制**: RBAC 权限管理
4. **防护措施**: 限流、防刷、SQL注入防护
5. **数据备份**: 定期备份和灾难恢复

## 📊 监控与运维

1. **应用监控**: 性能指标、错误追踪
2. **日志管理**: 结构化日志、日志聚合
3. **健康检查**: 服务健康状态监控
4. **告警机制**: 异常情况及时通知

这个后端架构设计为本地宝项目提供了完整的技术方案，支持高并发、高可用的生产环境部署，同时具备良好的扩展性和维护性。
